# Advanced Markdown Features Test 🚀

This document tests **ALL** the advanced markdown features now supported by the editor!

## Text Formatting

### Basic Emphasis
**This is bold text**
__This is also bold text__

*This is italic text*
_This is also italic text_

~~This is strikethrough text~~

### Enhanced Formatting
==This is highlighted text==
++This is inserted text++

### Subscript and Superscript
H~2~O (water molecule)
E = mc^2^ (Einstein's equation)
19^th^ century
CO~2~ emissions

### Keyboard Keys
Press [[Ctrl+C]] to copy and [[Ctrl+V]] to paste.
Use [[Alt+Tab]] to switch windows.

## Task Lists

- [x] Completed task
- [ ] Incomplete task
- [x] Another completed task
  - [ ] Nested incomplete task
  - [x] Nested completed task

## Lists

### Unordered Lists
- First item
- Second item
  - Nested item 1
  - Nested item 2
    - Deep nested item
- Third item

### Ordered Lists
1. First numbered item
2. Second numbered item
   1. Nested numbered item
   2. Another nested item
3. Third numbered item

## Code

### Inline Code
Here is some `inline code` in a sentence.

### Code Blocks
```javascript
function greetUser(name) {
  console.log(`Hello, ${name}!`);
  return `Welcome to the markdown editor!`;
}

// Call the function
greetUser("Developer");
```

```python
def calculate_fibonacci(n):
    if n <= 1:
        return n
    return calculate_fibonacci(n-1) + calculate_fibonacci(n-2)

# Generate first 10 Fibonacci numbers
for i in range(10):
    print(f"F({i}) = {calculate_fibonacci(i)}")
```

## Tables

### Basic Table
| Name | Age | City |
|------|-----|------|
| John | 25 | New York |
| Jane | 30 | London |
| Bob | 35 | Tokyo |

### Table with Alignment
| Left Aligned | Center Aligned | Right Aligned |
|:-------------|:--------------:|--------------:|
| Left text | Center text | Right text |
| More left | More center | More right |
| Final left | Final center | Final right |

## Links and Images

### Links
[External link to Google](https://www.google.com)
[Internal link](#text-formatting)

### Images
![Sample Image](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iIzAwNjZjYyIvPgogIDx0ZXh0IHg9IjE1MCIgeT0iMTAwIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTgiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+U2FtcGxlIEltYWdlPC90ZXh0Pgo8L3N2Zz4K)

## Blockquotes

> This is a simple blockquote.
> It can span multiple lines.

> **Note:** This is an important blockquote with **bold** text and *italic* text.

## Horizontal Rules

---

## Footnotes

Here's a sentence with a footnote[^1].

Another sentence with a footnote[^second].

Inline footnote^[This is an inline footnote] example.

[^1]: This is the first footnote.
[^second]: This is the second footnote with **bold** text.

## Definition Lists

Term 1
:   Definition 1 with lazy continuation.

Term 2 with *inline markup*
:   Definition 2

        { some code, part of Definition 2 }

    Third paragraph of definition 2.

_Compact style:_

Term 1
  ~ Definition 1

Term 2
  ~ Definition 2a
  ~ Definition 2b

## Abbreviations

This is an HTML abbreviation example.

It converts "HTML", but keeps intact partial entries like "xxxHTMLyyy" and so on.

*[HTML]: HyperText Markup Language

## Custom Containers

::: warning Important Warning
This is a warning container with important information!
:::

::: info Information
This is an info container with helpful details.
:::

::: tip Pro Tip
This is a tip container with useful advice.
:::

::: danger Danger Zone
This is a danger container with critical warnings!
:::

## Emojis

### Emoji Shortcodes
:smile: :heart: :thumbsup: :fire: :rocket: :star: :warning: :check: :x:
:wink: :cry: :laughing: :yum: :tada: :sparkles: :boom: :zap: :bulb:

### Emoticons
:-) :-( 8-) ;) :D :P

## Special Characters

### Typographic Replacements
(c) (r) (tm) (p)
+- (plus-minus)
... (ellipsis)
-- (en dash)
--- (em dash)

### Smart Quotes
"This is a quoted text"
'This is also quoted'

## Headings with Anchors

# Heading 1
## Heading 2
### Heading 3
#### Heading 4
##### Heading 5
###### Heading 6

## Mixed Content Test

This paragraph contains **bold**, *italic*, ~~strikethrough~~, ==highlighted==, ++inserted++, H~2~O, and E=mc^2^ text all in one sentence with `inline code` and a [link](https://example.com).

### Complex List with Mixed Content

1. **Bold item** with *italic* and ~~strikethrough~~
   - Nested item with ==highlight== and `code`
   - Another nested item with H~2~O and x^2^
2. Item with [link](https://example.com) and :smile: emoji
3. Final item with "smart quotes" and (c) symbol

---

## Advanced Features Summary

✅ **All Advanced Features Implemented:**

1. **Subscript/Superscript**: H~2~O, E=mc^2^
2. **Marked Text**: ==highlighted==
3. **Inserted Text**: ++inserted++
4. **Strikethrough**: ~~deleted~~
5. **Task Lists**: - [x] completed
6. **Footnotes**: [^1] with references
7. **Definition Lists**: Term : Definition
8. **Abbreviations**: HTML with tooltips
9. **Custom Containers**: ::: warning :::
10. **Keyboard Keys**: [[Ctrl+C]]
11. **Emojis**: :smile: :rocket: :fire:
12. **Typographic Replacements**: (c) (r) (tm)
13. **Smart Quotes**: "quoted" 'text'

🎉 **Test completed!** All advanced markdown features are now fully functional with proper styling. The synchronous scroll function has been completely removed as requested.
