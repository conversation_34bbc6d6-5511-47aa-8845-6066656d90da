/**
 * Enhanced Mobile Toolbar Component
 * Touch-friendly markdown editing tools with modern UX
 */

'use client';

import React, { useState, useRef } from 'react';

interface EnhancedMobileToolbarProps {
  onInsertMarkdown: (before: string, after: string) => void;
  onUndo: () => void;
  onRedo: () => void;
  canUndo: boolean;
  canRedo: boolean;
  className?: string;
}

interface ToolbarAction {
  id: string;
  icon: React.ReactNode;
  label: string;
  action: () => void;
  category: 'format' | 'insert' | 'action';
  shortcut?: string;
}

export function EnhancedMobileToolbar({
  onInsertMarkdown,
  onUndo,
  onRedo,
  canUndo,
  canRedo,
  className = ''
}: EnhancedMobileToolbarProps) {
  const [activeCategory, setActiveCategory] = useState<'format' | 'insert' | 'action'>('format');
  const scrollRef = useRef<HTMLDivElement>(null);

  // Haptic feedback simulation
  const triggerHaptic = (type: 'light' | 'medium' = 'light') => {
    if ('vibrate' in navigator) {
      navigator.vibrate(type === 'light' ? 10 : 25);
    }
  };

  // Toolbar actions organized by category
  const actions: ToolbarAction[] = [
    // Format category
    {
      id: 'bold',
      icon: <span className="font-bold text-lg">B</span>,
      label: 'Bold',
      action: () => onInsertMarkdown('**', '**'),
      category: 'format',
      shortcut: 'Ctrl+B'
    },
    {
      id: 'italic',
      icon: <span className="italic text-lg">I</span>,
      label: 'Italic',
      action: () => onInsertMarkdown('*', '*'),
      category: 'format',
      shortcut: 'Ctrl+I'
    },
    {
      id: 'strikethrough',
      icon: <span className="line-through text-lg">S</span>,
      label: 'Strike',
      action: () => onInsertMarkdown('~~', '~~'),
      category: 'format'
    },
    {
      id: 'code',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
        </svg>
      ),
      label: 'Code',
      action: () => onInsertMarkdown('`', '`'),
      category: 'format'
    },
    {
      id: 'highlight',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M18.5 4l-1.16-1.16a1.49 1.49 0 00-2.12 0L14 4.06l3.94 3.94 1.22-1.22a1.49 1.49 0 000-2.12zM12.78 5.28L3 15.06V19h3.94l9.78-9.78-3.94-3.94z"/>
        </svg>
      ),
      label: 'Highlight',
      action: () => onInsertMarkdown('==', '=='),
      category: 'format'
    },

    // Insert category
    {
      id: 'header1',
      icon: <span className="font-bold text-xl">H1</span>,
      label: 'Header 1',
      action: () => onInsertMarkdown('# ', ''),
      category: 'insert'
    },
    {
      id: 'header2',
      icon: <span className="font-bold text-lg">H2</span>,
      label: 'Header 2',
      action: () => onInsertMarkdown('## ', ''),
      category: 'insert'
    },
    {
      id: 'header3',
      icon: <span className="font-medium">H3</span>,
      label: 'Header 3',
      action: () => onInsertMarkdown('### ', ''),
      category: 'insert'
    },
    {
      id: 'link',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.102m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
        </svg>
      ),
      label: 'Link',
      action: () => onInsertMarkdown('[', '](url)'),
      category: 'insert',
      shortcut: 'Ctrl+K'
    },
    {
      id: 'image',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      ),
      label: 'Image',
      action: () => onInsertMarkdown('![alt](', ')'),
      category: 'insert'
    },
    {
      id: 'list',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
        </svg>
      ),
      label: 'List',
      action: () => onInsertMarkdown('- ', ''),
      category: 'insert'
    },
    {
      id: 'numbered-list',
      icon: <span className="font-bold text-lg">1.</span>,
      label: 'Numbered',
      action: () => onInsertMarkdown('1. ', ''),
      category: 'insert'
    },
    {
      id: 'quote',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
      ),
      label: 'Quote',
      action: () => onInsertMarkdown('> ', ''),
      category: 'insert'
    },
    {
      id: 'table',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M3 14h18m-9-4v8m-7 0V4a1 1 0 011-1h16a1 1 0 011 1v16a1 1 0 01-1 1H5a1 1 0 01-1-1V10z" />
        </svg>
      ),
      label: 'Table',
      action: () => onInsertMarkdown('| Header 1 | Header 2 |\n|----------|----------|\n| Cell 1   | Cell 2   |\n', ''),
      category: 'insert'
    },

    // Action category
    {
      id: 'undo',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
        </svg>
      ),
      label: 'Undo',
      action: onUndo,
      category: 'action',
      shortcut: 'Ctrl+Z'
    },
    {
      id: 'redo',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 10h-10a8 8 0 00-8 8v2m18-10l-6 6m6-6l-6-6" />
        </svg>
      ),
      label: 'Redo',
      action: onRedo,
      category: 'action',
      shortcut: 'Ctrl+Y'
    }
  ];

  const filteredActions = actions.filter(action => {
    if (action.id === 'undo' && !canUndo) return false;
    if (action.id === 'redo' && !canRedo) return false;
    return action.category === activeCategory;
  });

  const handleActionClick = (action: ToolbarAction) => {
    triggerHaptic('light');
    action.action();
  };

  const categories = [
    { id: 'format' as const, label: 'Format', icon: '🎨' },
    { id: 'insert' as const, label: 'Insert', icon: '➕' },
    { id: 'action' as const, label: 'Actions', icon: '⚡' }
  ];

  return (
    <div className={`bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Category Selector */}
      <div className="flex bg-gray-50 dark:bg-gray-700">
        {categories.map((category) => (
          <button
            key={category.id}
            onClick={() => {
              setActiveCategory(category.id);
              triggerHaptic('light');
            }}
            className={`flex-1 flex items-center justify-center gap-2 py-3 px-4 text-sm font-medium transition-all duration-200 ${
              activeCategory === category.id
                ? 'bg-white dark:bg-gray-600 text-blue-600 dark:text-blue-400 shadow-sm transform scale-105'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600'
            }`}
            style={{ minHeight: '44px' }}
          >
            <span className="text-lg">{category.icon}</span>
            <span className="font-medium">{category.label}</span>
          </button>
        ))}
      </div>

      {/* Action Buttons */}
      <div 
        ref={scrollRef}
        className="flex gap-2 p-3 overflow-x-auto scrollbar-hide"
        style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
      >
        {filteredActions.map((action) => (
          <button
            key={action.id}
            onClick={() => handleActionClick(action)}
            disabled={
              (action.id === 'undo' && !canUndo) ||
              (action.id === 'redo' && !canRedo)
            }
            className={`flex-shrink-0 flex flex-col items-center gap-2 p-3 rounded-xl transition-all duration-200 ${
              (action.id === 'undo' && !canUndo) || (action.id === 'redo' && !canRedo)
                ? 'opacity-50 cursor-not-allowed bg-gray-100 dark:bg-gray-700'
                : 'hover:bg-blue-50 dark:hover:bg-blue-900/20 active:bg-blue-100 dark:active:bg-blue-900/30 hover:shadow-md active:scale-95'
            }`}
            style={{ minWidth: '70px', minHeight: '70px' }}
          >
            <div className={`flex items-center justify-center w-10 h-10 rounded-lg transition-colors ${
              (action.id === 'undo' && !canUndo) || (action.id === 'redo' && !canRedo)
                ? 'text-gray-400 dark:text-gray-500'
                : 'text-gray-700 dark:text-gray-300 group-hover:text-blue-600 dark:group-hover:text-blue-400'
            }`}>
              {action.icon}
            </div>
            <span className={`text-xs font-medium text-center leading-tight ${
              (action.id === 'undo' && !canUndo) || (action.id === 'redo' && !canRedo)
                ? 'text-gray-400 dark:text-gray-500'
                : 'text-gray-600 dark:text-gray-400'
            }`}>
              {action.label}
            </span>
            {action.shortcut && (
              <span className="text-xs text-gray-400 dark:text-gray-500 opacity-75">
                {action.shortcut.replace('Ctrl', '⌘')}
              </span>
            )}
          </button>
        ))}
      </div>

      {/* Gesture Indicator */}
      <div className="flex justify-center py-2">
        <div className="w-12 h-1 bg-gray-300 dark:bg-gray-600 rounded-full" />
      </div>
    </div>
  );
}
