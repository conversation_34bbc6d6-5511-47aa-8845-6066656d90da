/**
 * Performance Optimizations for 100+ Concurrent Users
 * Memory management, caching, and stability improvements
 */

'use client';

// Memory management utilities
export class MemoryManager {
  private static instance: MemoryManager;
  private memoryUsage: Map<string, number> = new Map();
  private cleanupTasks: Map<string, () => void> = new Map();
  private maxMemoryThreshold = 50 * 1024 * 1024; // 50MB

  static getInstance(): MemoryManager {
    if (!MemoryManager.instance) {
      MemoryManager.instance = new MemoryManager();
    }
    return MemoryManager.instance;
  }

  // Track memory usage for components
  trackMemoryUsage(componentId: string, size: number): void {
    this.memoryUsage.set(componentId, size);
    this.checkMemoryThreshold();
  }

  // Register cleanup task for component
  registerCleanup(componentId: string, cleanup: () => void): void {
    this.cleanupTasks.set(componentId, cleanup);
  }

  // Clean up component memory
  cleanup(componentId: string): void {
    const cleanup = this.cleanupTasks.get(componentId);
    if (cleanup) {
      cleanup();
      this.cleanupTasks.delete(componentId);
      this.memoryUsage.delete(componentId);
    }
  }

  // Check if memory usage exceeds threshold
  private checkMemoryThreshold(): void {
    const totalMemory = Array.from(this.memoryUsage.values()).reduce((sum, size) => sum + size, 0);
    
    if (totalMemory > this.maxMemoryThreshold) {
      this.performGarbageCollection();
    }
  }

  // Perform garbage collection
  private performGarbageCollection(): void {
    // Clean up oldest components first
    const sortedComponents = Array.from(this.memoryUsage.entries())
      .sort(([, a], [, b]) => b - a)
      .slice(0, Math.floor(this.memoryUsage.size / 2));

    sortedComponents.forEach(([componentId]) => {
      this.cleanup(componentId);
    });

    // Force garbage collection if available
    if ('gc' in window && typeof (window as any).gc === 'function') {
      (window as any).gc();
    }
  }

  // Get current memory usage
  getMemoryUsage(): { total: number; components: number } {
    const total = Array.from(this.memoryUsage.values()).reduce((sum, size) => sum + size, 0);
    return { total, components: this.memoryUsage.size };
  }
}

// Debounce utility for performance
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate = false
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    
    const callNow = immediate && !timeout;
    
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func(...args);
  };
}

// Throttle utility for performance
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// Virtual scrolling for large lists
export class VirtualScrollManager {
  private containerHeight: number;
  private itemHeight: number;
  private totalItems: number;
  private visibleStart: number = 0;
  private visibleEnd: number = 0;
  private buffer: number = 5;

  constructor(containerHeight: number, itemHeight: number, totalItems: number) {
    this.containerHeight = containerHeight;
    this.itemHeight = itemHeight;
    this.totalItems = totalItems;
    this.calculateVisibleRange(0);
  }

  calculateVisibleRange(scrollTop: number): { start: number; end: number; offsetY: number } {
    const visibleStart = Math.floor(scrollTop / this.itemHeight);
    const visibleEnd = Math.min(
      visibleStart + Math.ceil(this.containerHeight / this.itemHeight),
      this.totalItems
    );

    this.visibleStart = Math.max(0, visibleStart - this.buffer);
    this.visibleEnd = Math.min(this.totalItems, visibleEnd + this.buffer);

    return {
      start: this.visibleStart,
      end: this.visibleEnd,
      offsetY: this.visibleStart * this.itemHeight
    };
  }

  getTotalHeight(): number {
    return this.totalItems * this.itemHeight;
  }

  getVisibleItems(): { start: number; end: number } {
    return { start: this.visibleStart, end: this.visibleEnd };
  }
}

// Caching system for markdown processing
export class MarkdownCache {
  private static instance: MarkdownCache;
  private cache: Map<string, { html: string; timestamp: number }> = new Map();
  private maxCacheSize = 100;
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes

  static getInstance(): MarkdownCache {
    if (!MarkdownCache.instance) {
      MarkdownCache.instance = new MarkdownCache();
    }
    return MarkdownCache.instance;
  }

  // Generate cache key from markdown content
  private generateKey(markdown: string): string {
    // Simple hash function for cache key
    let hash = 0;
    for (let i = 0; i < markdown.length; i++) {
      const char = markdown.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString();
  }

  // Get cached HTML
  get(markdown: string): string | null {
    const key = this.generateKey(markdown);
    const cached = this.cache.get(key);
    
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.html;
    }
    
    if (cached) {
      this.cache.delete(key);
    }
    
    return null;
  }

  // Set cached HTML
  set(markdown: string, html: string): void {
    const key = this.generateKey(markdown);
    
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.maxCacheSize) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }
    
    this.cache.set(key, { html, timestamp: Date.now() });
  }

  // Clear cache
  clear(): void {
    this.cache.clear();
  }

  // Get cache statistics
  getStats(): { size: number; maxSize: number; hitRate: number } {
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      hitRate: 0 // Would need to track hits/misses for accurate rate
    };
  }
}

// Connection pool for handling multiple users
export class ConnectionPool {
  private static instance: ConnectionPool;
  private connections: Map<string, WebSocket> = new Map();
  private maxConnections = 150;
  private heartbeatInterval = 30000; // 30 seconds
  private heartbeatTimers: Map<string, NodeJS.Timeout> = new Map();

  static getInstance(): ConnectionPool {
    if (!ConnectionPool.instance) {
      ConnectionPool.instance = new ConnectionPool();
    }
    return ConnectionPool.instance;
  }

  // Add connection to pool
  addConnection(userId: string, ws: WebSocket): boolean {
    if (this.connections.size >= this.maxConnections) {
      return false; // Pool is full
    }

    this.connections.set(userId, ws);
    this.startHeartbeat(userId, ws);
    return true;
  }

  // Remove connection from pool
  removeConnection(userId: string): void {
    const timer = this.heartbeatTimers.get(userId);
    if (timer) {
      clearInterval(timer);
      this.heartbeatTimers.delete(userId);
    }
    this.connections.delete(userId);
  }

  // Start heartbeat for connection
  private startHeartbeat(userId: string, ws: WebSocket): void {
    const timer = setInterval(() => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.ping();
      } else {
        this.removeConnection(userId);
      }
    }, this.heartbeatInterval);

    this.heartbeatTimers.set(userId, timer);
  }

  // Get connection statistics
  getStats(): { active: number; max: number; utilization: number } {
    return {
      active: this.connections.size,
      max: this.maxConnections,
      utilization: (this.connections.size / this.maxConnections) * 100
    };
  }

  // Broadcast message to all connections
  broadcast(message: string, excludeUserId?: string): void {
    this.connections.forEach((ws, userId) => {
      if (userId !== excludeUserId && ws.readyState === WebSocket.OPEN) {
        ws.send(message);
      }
    });
  }
}

// Performance monitoring
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, number[]> = new Map();
  private maxMetricHistory = 100;

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  // Record performance metric
  recordMetric(name: string, value: number): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }

    const values = this.metrics.get(name)!;
    values.push(value);

    // Keep only recent metrics
    if (values.length > this.maxMetricHistory) {
      values.shift();
    }
  }

  // Get metric statistics
  getMetricStats(name: string): { avg: number; min: number; max: number; count: number } | null {
    const values = this.metrics.get(name);
    if (!values || values.length === 0) {
      return null;
    }

    const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
    const min = Math.min(...values);
    const max = Math.max(...values);

    return { avg, min, max, count: values.length };
  }

  // Measure function execution time
  measureExecution<T>(name: string, fn: () => T): T {
    const start = performance.now();
    const result = fn();
    const end = performance.now();
    
    this.recordMetric(name, end - start);
    return result;
  }

  // Get all metrics
  getAllMetrics(): Record<string, { avg: number; min: number; max: number; count: number }> {
    const result: Record<string, any> = {};
    
    this.metrics.forEach((_, name) => {
      const stats = this.getMetricStats(name);
      if (stats) {
        result[name] = stats;
      }
    });

    return result;
  }
}

// Resource cleanup utilities
export function scheduleCleanup(callback: () => void, delay: number): () => void {
  const timeoutId = setTimeout(callback, delay);
  return () => clearTimeout(timeoutId);
}

export function createResourceManager() {
  const resources: (() => void)[] = [];

  return {
    add: (cleanup: () => void) => {
      resources.push(cleanup);
    },
    cleanup: () => {
      resources.forEach(cleanup => {
        try {
          cleanup();
        } catch (error) {
          console.error('Error during resource cleanup:', error);
        }
      });
      resources.length = 0;
    }
  };
}

// Initialize performance optimizations
export function initializePerformanceOptimizations(): void {
  // Initialize singletons
  MemoryManager.getInstance();
  MarkdownCache.getInstance();
  ConnectionPool.getInstance();
  PerformanceMonitor.getInstance();

  // Set up global error handling
  window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
    PerformanceMonitor.getInstance().recordMetric('errors', 1);
  });

  // Set up unhandled promise rejection handling
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    PerformanceMonitor.getInstance().recordMetric('promise_rejections', 1);
  });

  // Monitor memory usage
  if ('memory' in performance) {
    setInterval(() => {
      const memory = (performance as any).memory;
      PerformanceMonitor.getInstance().recordMetric('memory_used', memory.usedJSHeapSize);
      PerformanceMonitor.getInstance().recordMetric('memory_total', memory.totalJSHeapSize);
    }, 10000); // Every 10 seconds
  }

  console.log('Performance optimizations initialized');
}
