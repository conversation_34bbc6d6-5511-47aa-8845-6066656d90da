/**
 * Enhanced Markdown Styles
 * Comprehensive styling for advanced markdown features
 */

/* Import KaTeX CSS for math rendering */
@import 'katex/dist/katex.min.css';

/* Import Highlight.js CSS for syntax highlighting */
@import 'highlight.js/styles/github.css';

/* Math expressions with enhanced styling */
.math-display,
.katex-display {
  margin: 1.5rem 0;
  text-align: center;
  overflow-x: auto;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  -webkit-overflow-scrolling: touch;
}

.dark .math-display,
.dark .katex-display {
  background: #1f2937;
  border-color: #374151;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.math-inline,
.katex-inline {
  display: inline-block;
  margin: 0 0.1rem;
  vertical-align: baseline;
}

.math-error {
  color: #dc3545;
  background: #f8d7da;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.9em;
  border: 1px solid #f5c6cb;
  display: inline-block;
}

.dark .math-error {
  color: #f87171;
  background: #7f1d1d;
  border-color: #991b1b;
}

/* KaTeX specific overrides */
.katex {
  font-size: 1.1em;
}

.katex-display .katex {
  font-size: 1.2em;
}

/* Ensure KaTeX fonts load properly */
.katex .katex-mathml {
  position: absolute;
  clip: rect(1px, 1px, 1px, 1px);
  padding: 0;
  border: 0;
  height: 1px;
  width: 1px;
  overflow: hidden;
}

/* Mobile math optimizations */
@media (max-width: 768px) {
  .math-display,
  .katex-display {
    margin: 1rem 0;
    padding: 0.75rem;
    font-size: 0.9em;
  }

  .katex {
    font-size: 1em;
  }

  .katex-display .katex {
    font-size: 1.1em;
  }
}

/* Print styles for math */
@media print {
  .math-display,
  .katex-display {
    background: white !important;
    border: 1px solid #000 !important;
    box-shadow: none !important;
    page-break-inside: avoid;
  }

  .math-error {
    background: #f0f0f0 !important;
    color: #000 !important;
    border-color: #000 !important;
  }
}

/* Mermaid diagrams */
.mermaid-diagram {
  margin: 2rem 0;
  text-align: center;
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  overflow-x: auto;
}

.mermaid-rendered svg {
  max-width: 100%;
  height: auto;
}

.diagram-error {
  color: #dc3545;
  background: #f8d7da;
  padding: 1rem;
  border-radius: 4px;
  font-family: monospace;
}

/* Custom containers */
.custom-container {
  margin: 1.5rem 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.container-header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  font-weight: 600;
  gap: 0.5rem;
}

.container-icon {
  font-size: 1.2em;
}

.container-title {
  flex: 1;
}

.container-content {
  padding: 1rem;
}

.custom-container.note {
  border-left: 4px solid #0ea5e9;
}

.custom-container.note .container-header {
  background: #e0f2fe;
  color: #0369a1;
}

.custom-container.note .container-content {
  background: #f0f9ff;
}

.custom-container.warning {
  border-left: 4px solid #f59e0b;
}

.custom-container.warning .container-header {
  background: #fef3c7;
  color: #d97706;
}

.custom-container.warning .container-content {
  background: #fffbeb;
}

.custom-container.info {
  border-left: 4px solid #06b6d4;
}

.custom-container.info .container-header {
  background: #cffafe;
  color: #0891b2;
}

.custom-container.info .container-content {
  background: #f0fdfa;
}

.custom-container.tip {
  border-left: 4px solid #10b981;
}

.custom-container.tip .container-header {
  background: #d1fae5;
  color: #059669;
}

.custom-container.tip .container-content {
  background: #ecfdf5;
}

.custom-container.danger {
  border-left: 4px solid #ef4444;
}

.custom-container.danger .container-header {
  background: #fee2e2;
  color: #dc2626;
}

.custom-container.danger .container-content {
  background: #fef2f2;
}

.custom-container.success {
  border-left: 4px solid #22c55e;
}

.custom-container.success .container-header {
  background: #dcfce7;
  color: #16a34a;
}

.custom-container.success .container-content {
  background: #f0fdf4;
}

/* Enhanced code blocks */
.enhanced-code-block {
  margin: 1.5rem 0;
  border-radius: 8px;
  overflow: hidden;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  background: #e9ecef;
  border-bottom: 1px solid #dee2e6;
}

.code-language {
  font-size: 0.875rem;
  font-weight: 600;
  color: #495057;
  text-transform: uppercase;
}

.code-actions {
  display: flex;
  gap: 0.5rem;
}

.copy-code-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.copy-code-btn:hover {
  background: #5a6268;
}

.copy-icon {
  font-size: 0.875em;
}

.code-content {
  margin: 0;
  padding: 1rem;
  overflow-x: auto;
  background: #ffffff;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

.code-line {
  display: block;
  position: relative;
  padding-left: 3rem;
}

.code-line::before {
  content: attr(data-line);
  position: absolute;
  left: 0;
  width: 2.5rem;
  text-align: right;
  color: #6c757d;
  font-size: 0.75rem;
  user-select: none;
}

/* Enhanced task lists */
.task-list-checkbox {
  margin-right: 0.5rem;
  cursor: pointer;
}

.task-completed {
  accent-color: #22c55e;
}

.task-pending {
  accent-color: #6b7280;
}

.task-in-progress {
  accent-color: #f59e0b;
}

.task-cancelled {
  accent-color: #ef4444;
}

/* Enhanced tables with mobile optimization */
.table-wrapper {
  margin: 1.5rem 0;
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.table-wrapper::-webkit-scrollbar {
  height: 8px;
}

.table-wrapper::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

.enhanced-table {
  width: 100%;
  min-width: 500px; /* Ensure minimum width for readability */
  border-collapse: collapse;
  background: white;
  font-size: 0.875rem;
}

.table-header {
  background: #f8f9fa;
  position: sticky;
  top: 0;
  z-index: 10;
}

.table-header-cell {
  padding: 0.75rem;
  text-align: left;
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
  white-space: nowrap;
  min-width: 100px;
}

.table-cell {
  padding: 0.75rem;
  border-bottom: 1px solid #e9ecef;
  word-wrap: break-word;
  max-width: 200px;
}

.enhanced-table tr:hover {
  background: #f8f9fa;
}

/* Mobile table optimizations */
@media (max-width: 768px) {
  .table-wrapper {
    margin: 1rem 0;
    border-radius: 6px;
    font-size: 0.8rem;
  }

  .enhanced-table {
    min-width: 600px; /* Wider minimum for mobile */
    font-size: 0.8rem;
  }

  .table-header-cell,
  .table-cell {
    padding: 0.5rem;
    min-width: 80px;
  }

  .table-cell {
    max-width: 150px;
  }
}

/* Enhanced lists */
.enhanced-list {
  margin: 1rem 0;
  padding-left: 2rem;
}

.list-item {
  margin: 0.5rem 0;
  line-height: 1.6;
}

/* Enhanced blockquotes */
.enhanced-blockquote {
  margin: 1.5rem 0;
  padding: 1rem 1.5rem;
  border-left: 4px solid #6c757d;
  background: #f8f9fa;
  font-style: italic;
  color: #495057;
}

/* Enhanced links */
.enhanced-link {
  color: #0d6efd;
  text-decoration: none;
  transition: color 0.2s;
}

.enhanced-link:hover {
  color: #0a58ca;
  text-decoration: underline;
}

.external-link-icon {
  font-size: 0.8em;
  opacity: 0.7;
  margin-left: 0.2rem;
}

/* Enhanced headers */
.enhanced-header {
  position: relative;
  margin: 2rem 0 1rem 0;
  font-weight: 600;
  line-height: 1.2;
}

.header-anchor {
  position: absolute;
  left: -1.5rem;
  opacity: 0;
  color: #6c757d;
  text-decoration: none;
  font-weight: normal;
  transition: opacity 0.2s;
}

.enhanced-header:hover .header-anchor {
  opacity: 1;
}

.header-1 { font-size: 2.5rem; color: #212529; }
.header-2 { font-size: 2rem; color: #343a40; }
.header-3 { font-size: 1.5rem; color: #495057; }
.header-4 { font-size: 1.25rem; color: #6c757d; }
.header-5 { font-size: 1.1rem; color: #6c757d; }
.header-6 { font-size: 1rem; color: #6c757d; }

/* Enhanced paragraphs */
.enhanced-paragraph {
  margin: 1rem 0;
  line-height: 1.6;
  color: #212529;
}

/* Text formatting */
.md-highlight {
  background: #fff3cd;
  padding: 0.1rem 0.2rem;
  border-radius: 2px;
}

.md-inserted {
  background: #d1ecf1;
  text-decoration: none;
  padding: 0.1rem 0.2rem;
  border-radius: 2px;
}

.md-subscript {
  font-size: 0.8em;
}

.md-superscript {
  font-size: 0.8em;
}

.md-kbd {
  background: #e9ecef;
  border: 1px solid #ced4da;
  border-radius: 3px;
  padding: 0.1rem 0.3rem;
  font-family: monospace;
  font-size: 0.9em;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Progress bars */
.progress-bar {
  position: relative;
  width: 100%;
  height: 1.5rem;
  background: #e9ecef;
  border-radius: 0.75rem;
  overflow: hidden;
  margin: 1rem 0;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #0d6efd, #0a58ca);
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Footnotes */
.footnote-ref {
  color: #0d6efd;
  text-decoration: none;
  font-weight: 600;
}

.footnote {
  margin: 0.5rem 0;
  padding: 0.5rem;
  background: #f8f9fa;
  border-left: 3px solid #6c757d;
  font-size: 0.9em;
}

.footnote-backref {
  color: #6c757d;
  text-decoration: none;
  margin-right: 0.5rem;
}

/* Definition lists */
.definition-list {
  margin: 1rem 0;
}

.definition-list dt {
  font-weight: 600;
  margin-top: 1rem;
}

.definition-list dd {
  margin-left: 2rem;
  margin-bottom: 0.5rem;
}

/* Responsive design with comprehensive mobile optimizations */
@media (max-width: 768px) {
  /* Typography adjustments */
  .enhanced-header {
    font-size: 1.5rem;
    margin: 1.5rem 0 1rem 0;
  }

  .header-1 { font-size: 2rem; }
  .header-2 { font-size: 1.75rem; }
  .header-3 { font-size: 1.5rem; }
  .header-4 { font-size: 1.25rem; }
  .header-5 { font-size: 1.1rem; }
  .header-6 { font-size: 1rem; }

  .header-anchor {
    display: none;
  }

  .enhanced-paragraph {
    font-size: 0.95rem;
    line-height: 1.7;
    margin: 0.75rem 0;
  }

  /* Code blocks mobile optimization */
  .enhanced-code-block {
    margin: 1rem 0;
    border-radius: 6px;
    font-size: 0.8rem;
  }

  .code-content {
    font-size: 0.75rem;
    padding: 0.75rem;
    line-height: 1.4;
  }

  .code-header {
    padding: 0.4rem 0.75rem;
  }

  .code-language {
    font-size: 0.75rem;
  }

  .copy-code-btn {
    padding: 0.2rem 0.4rem;
    font-size: 0.7rem;
  }

  /* Lists mobile optimization */
  .enhanced-list {
    margin: 0.75rem 0;
    padding-left: 1.5rem;
  }

  .list-item {
    margin: 0.4rem 0;
    line-height: 1.6;
  }

  /* Blockquotes mobile optimization */
  .enhanced-blockquote {
    margin: 1rem 0;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  /* Custom containers mobile optimization */
  .custom-container {
    margin: 1rem 0;
    border-radius: 6px;
  }

  .container-header {
    padding: 0.6rem 0.75rem;
    font-size: 0.9rem;
  }

  .container-content {
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  .container-icon {
    font-size: 1.1em;
  }

  /* Math expressions mobile optimization */
  .math-display {
    margin: 1rem 0;
    padding: 0.75rem;
    border-radius: 6px;
    font-size: 0.9rem;
    overflow-x: auto;
  }

  .math-inline {
    font-size: 0.9rem;
  }

  /* Mermaid diagrams mobile optimization */
  .mermaid-diagram {
    margin: 1.5rem 0;
    padding: 0.75rem;
    border-radius: 6px;
    overflow-x: auto;
  }

  .mermaid-rendered svg {
    max-width: 100%;
    height: auto;
    min-width: 300px;
  }

  /* Progress bars mobile optimization */
  .progress-bar {
    height: 1.25rem;
    margin: 0.75rem 0;
    border-radius: 0.625rem;
  }

  .progress-text {
    font-size: 0.7rem;
  }

  /* Footnotes mobile optimization */
  .footnote {
    margin: 0.4rem 0;
    padding: 0.4rem;
    font-size: 0.85rem;
  }

  /* Definition lists mobile optimization */
  .definition-list dt {
    margin-top: 0.75rem;
    font-size: 0.9rem;
  }

  .definition-list dd {
    margin-left: 1.5rem;
    margin-bottom: 0.4rem;
    font-size: 0.9rem;
  }
}

/* Ultra-small mobile screens */
@media (max-width: 480px) {
  .enhanced-header {
    font-size: 1.25rem;
  }

  .header-1 { font-size: 1.75rem; }
  .header-2 { font-size: 1.5rem; }
  .header-3 { font-size: 1.25rem; }

  .enhanced-paragraph {
    font-size: 0.9rem;
  }

  .code-content {
    font-size: 0.7rem;
    padding: 0.5rem;
  }

  .enhanced-list {
    padding-left: 1.25rem;
  }

  .custom-container {
    margin: 0.75rem 0;
  }

  .container-header {
    padding: 0.5rem;
    font-size: 0.85rem;
  }

  .container-content {
    padding: 0.5rem;
    font-size: 0.85rem;
  }
}
