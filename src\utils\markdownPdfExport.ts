/**
 * Markdown-PDF export using dedicated markdown-pdf library
 * Provides direct markdown to PDF conversion with good quality and text selection
 */

import markdownPdf from 'markdown-pdf';
import { promisify } from 'util';

export interface MarkdownPdfOptions {
  title?: string;
  author?: string;
  subject?: string;
  keywords?: string;
  format?: 'A4' | 'A3' | 'A5' | 'Letter' | 'Legal';
  orientation?: 'portrait' | 'landscape';
  border?: {
    top?: string;
    right?: string;
    bottom?: string;
    left?: string;
  };
  quality?: 'high' | 'medium' | 'low';
  timeout?: number;
  cssPath?: string;
  highlightCssPath?: string;
  paperBorder?: string;
  renderDelay?: number;
  loadTimeout?: number;
}

export class MarkdownPdfExporter {
  private options: Required<MarkdownPdfOptions>;

  constructor(options: MarkdownPdfOptions = {}) {
    this.options = {
      title: options.title || 'Markdown Document',
      author: options.author || 'Markdown Editor',
      subject: options.subject || 'Exported Document',
      keywords: options.keywords || 'markdown, export, pdf',
      format: options.format || 'A4',
      orientation: options.orientation || 'portrait',
      border: {
        top: options.border?.top || '1in',
        right: options.border?.right || '1in',
        bottom: options.border?.bottom || '1in',
        left: options.border?.left || '1in',
      },
      quality: options.quality || 'high',
      timeout: options.timeout || 30000,
      cssPath: options.cssPath || '',
      highlightCssPath: options.highlightCssPath || '',
      paperBorder: options.paperBorder || '1in',
      renderDelay: options.renderDelay || 1000,
      loadTimeout: options.loadTimeout || 10000,
    };
  }

  private getCustomCss(): string {
    return `
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        font-size: 12px;
        line-height: 1.6;
        color: #333;
        background: white;
        margin: 0;
        padding: 20px;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      
      h1, h2, h3, h4, h5, h6 {
        margin: 1.5em 0 0.5em 0;
        font-weight: 600;
        line-height: 1.2;
        page-break-after: avoid;
        color: #2c3e50;
      }
      
      h1 { 
        font-size: 2em; 
        border-bottom: 2px solid #3498db; 
        padding-bottom: 0.3em; 
      }
      h2 { 
        font-size: 1.5em; 
        border-bottom: 1px solid #bdc3c7; 
        padding-bottom: 0.2em; 
      }
      h3 { font-size: 1.25em; }
      h4 { font-size: 1.1em; }
      h5 { font-size: 1em; font-weight: 700; }
      h6 { font-size: 0.9em; font-weight: 700; color: #7f8c8d; }
      
      p {
        margin: 0.8em 0;
        text-align: justify;
        hyphens: auto;
      }
      
      blockquote {
        margin: 1em 0;
        padding: 0.5em 1em;
        border-left: 4px solid #3498db;
        background: #f8f9fa;
        font-style: italic;
        page-break-inside: avoid;
      }
      
      pre {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 1em;
        margin: 1em 0;
        overflow-x: auto;
        page-break-inside: avoid;
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        font-size: 0.9em;
        white-space: pre-wrap;
        word-wrap: break-word;
      }
      
      code {
        background: #f1f3f4;
        padding: 0.2em 0.4em;
        border-radius: 3px;
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        font-size: 0.9em;
        word-break: break-word;
      }
      
      pre code {
        background: none;
        padding: 0;
      }
      
      table {
        width: 100%;
        border-collapse: collapse;
        margin: 1em 0;
        page-break-inside: avoid;
        font-size: 0.9em;
      }
      
      th, td {
        border: 1px solid #ddd;
        padding: 0.5em;
        text-align: left;
        vertical-align: top;
      }
      
      th {
        background: #f8f9fa;
        font-weight: 600;
      }
      
      ul, ol {
        margin: 0.8em 0;
        padding-left: 2em;
      }
      
      li {
        margin: 0.3em 0;
      }
      
      img {
        max-width: 100%;
        height: auto;
        page-break-inside: avoid;
        display: block;
        margin: 1em auto;
      }
      
      hr {
        border: none;
        border-top: 2px solid #e9ecef;
        margin: 2em 0;
        page-break-after: avoid;
      }
      
      a {
        color: #3498db;
        text-decoration: none;
        word-break: break-word;
      }
      
      a:hover {
        text-decoration: underline;
      }
      
      .page-break {
        page-break-before: always;
      }
      
      /* Syntax highlighting */
      .hljs {
        background: #f8f9fa;
        color: #333;
      }
      
      .hljs-keyword,
      .hljs-selector-tag,
      .hljs-built_in,
      .hljs-name,
      .hljs-tag {
        color: #d73a49;
      }
      
      .hljs-string,
      .hljs-attr,
      .hljs-selector-attr,
      .hljs-selector-pseudo,
      .hljs-template-tag {
        color: #032f62;
      }
      
      .hljs-comment,
      .hljs-quote {
        color: #6a737d;
        font-style: italic;
      }
      
      .hljs-number,
      .hljs-literal,
      .hljs-variable,
      .hljs-template-variable,
      .hljs-attribute {
        color: #005cc5;
      }
      
      @media print {
        body {
          font-size: 12px;
        }
        
        h1, h2, h3, h4, h5, h6 {
          page-break-after: avoid;
        }
        
        pre, blockquote, table, img {
          page-break-inside: avoid;
        }
        
        p, li {
          orphans: 2;
          widows: 2;
        }
        
        a[href]:after {
          content: " (" attr(href) ")";
          font-size: 0.8em;
          color: #666;
        }
      }
    `;
  }

  private getMarkdownPdfOptions() {
    const paperFormat = this.options.format.toLowerCase();
    
    return {
      paperFormat: paperFormat,
      paperOrientation: this.options.orientation,
      paperBorder: this.options.paperBorder,
      renderDelay: this.options.renderDelay,
      loadTimeout: this.options.loadTimeout,
      cssPath: this.options.cssPath,
      highlightCssPath: this.options.highlightCssPath,
      remarkable: {
        html: true,
        breaks: true,
        typographer: true,
        linkify: true,
        langPrefix: 'hljs language-',
      },
      preProcessMd: (markdown: string) => {
        // Add title if provided
        if (this.options.title && !markdown.startsWith('#')) {
          return `# ${this.options.title}\n\n${markdown}`;
        }
        return markdown;
      },
      preProcessHtml: (html: string) => {
        // Add custom CSS
        const customCss = this.getCustomCss();
        return html.replace('</head>', `<style>${customCss}</style></head>`);
      },
    };
  }

  public async exportMarkdownToPdf(markdown: string): Promise<Uint8Array> {
    return new Promise((resolve, reject) => {
      const options = this.getMarkdownPdfOptions();
      
      // Create a buffer to collect PDF data
      const chunks: Buffer[] = [];
      
      const pdfStream = markdownPdf(options);
      
      pdfStream.on('data', (chunk: Buffer) => {
        chunks.push(chunk);
      });
      
      pdfStream.on('end', () => {
        const pdfBuffer = Buffer.concat(chunks);
        resolve(new Uint8Array(pdfBuffer));
      });
      
      pdfStream.on('error', (error: Error) => {
        console.error('Error generating PDF with markdown-pdf:', error);
        reject(new Error(`Failed to generate PDF: ${error.message}`));
      });
      
      // Write markdown to the stream
      pdfStream.write(markdown);
      pdfStream.end();
    });
  }

  public getPdfBlob(pdfData: Uint8Array): Blob {
    return new Blob([pdfData], { type: 'application/pdf' });
  }

  public downloadPdf(pdfData: Uint8Array, filename: string): void {
    const blob = this.getPdfBlob(pdfData);
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = filename.replace(/\.[^/.]+$/, '.pdf');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Clean up
    setTimeout(() => URL.revokeObjectURL(url), 1000);
  }
}

/**
 * Convenience function for quick PDF export using markdown-pdf
 */
export async function exportMarkdownToPdfWithMarkdownPdf(
  markdown: string,
  filename: string,
  options: MarkdownPdfOptions = {}
): Promise<void> {
  const exporter = new MarkdownPdfExporter(options);
  const pdfData = await exporter.exportMarkdownToPdf(markdown);
  exporter.downloadPdf(pdfData, filename);
}
