/**
 * Enhanced PDF Export with optimized file size and better quality
 * Uses HTML-to-PDF conversion with better compression and layout
 */

import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import { processMarkdown } from './markdownProcessor';

interface PdfExportOptions {
  title?: string;
  author?: string;
  subject?: string;
  keywords?: string;
  fontSize?: number;
  lineHeight?: number;
  margin?: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  pageSize?: 'a4' | 'letter' | 'legal';
  orientation?: 'portrait' | 'landscape';
  quality?: number; // 0.1 to 1.0
  compression?: boolean;
}

interface PdfMetrics {
  pageWidth: number;
  pageHeight: number;
  contentWidth: number;
  contentHeight: number;
  margin: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
}

export class EnhancedPdfExporter {
  private options: Required<PdfExportOptions>;
  private doc: jsPDF;
  private metrics: PdfMetrics;

  constructor(options: PdfExportOptions = {}) {
    this.options = {
      title: options.title || 'Markdown Document',
      author: options.author || 'Markdown Editor',
      subject: options.subject || 'Exported Document',
      keywords: options.keywords || 'markdown, export, pdf',
      fontSize: options.fontSize || 12,
      lineHeight: options.lineHeight || 1.4,
      margin: options.margin || { top: 20, right: 20, bottom: 20, left: 20 },
      pageSize: options.pageSize || 'a4',
      orientation: options.orientation || 'portrait',
      quality: options.quality || 0.8,
      compression: options.compression !== false
    };

    this.initializePdf();
    this.calculateMetrics();
  }

  private initializePdf(): void {
    this.doc = new jsPDF({
      orientation: this.options.orientation,
      unit: 'mm',
      format: this.options.pageSize,
      compress: this.options.compression
    });

    // Set document metadata
    this.doc.setProperties({
      title: this.options.title,
      subject: this.options.subject,
      author: this.options.author,
      keywords: this.options.keywords,
      creator: 'Enhanced Markdown Editor'
    });
  }

  private calculateMetrics(): void {
    const pageSize = this.doc.internal.pageSize;
    this.metrics = {
      pageWidth: pageSize.getWidth(),
      pageHeight: pageSize.getHeight(),
      contentWidth: pageSize.getWidth() - this.options.margin.left - this.options.margin.right,
      contentHeight: pageSize.getHeight() - this.options.margin.top - this.options.margin.bottom,
      margin: this.options.margin
    };
  }

  /**
   * Export markdown to PDF using optimized HTML rendering
   */
  public async exportMarkdownToPdf(markdown: string): Promise<Uint8Array> {
    try {
      // Process markdown to HTML
      const htmlContent = processMarkdown(markdown);
      
      // Create optimized HTML for PDF rendering
      const optimizedHtml = this.createOptimizedHtml(htmlContent);
      
      // Render HTML to PDF using canvas-based approach
      await this.renderHtmlToPdf(optimizedHtml);
      
      return this.doc.output('arraybuffer');
    } catch (error) {
      console.error('Error exporting to PDF:', error);
      throw new Error('Failed to export PDF');
    }
  }

  private createOptimizedHtml(content: string): string {
    const styles = `
      <style>
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          font-size: ${this.options.fontSize}px;
          line-height: ${this.options.lineHeight};
          color: #333;
          max-width: ${this.metrics.contentWidth * 3.78}px; /* Convert mm to px */
          margin: 0 auto;
          padding: 20px;
          background: white;
        }
        
        h1, h2, h3, h4, h5, h6 {
          margin: 1.5em 0 0.5em 0;
          font-weight: 600;
          line-height: 1.2;
          page-break-after: avoid;
        }
        
        h1 { font-size: 2em; color: #2c3e50; }
        h2 { font-size: 1.5em; color: #34495e; }
        h3 { font-size: 1.25em; color: #34495e; }
        h4 { font-size: 1.1em; color: #34495e; }
        h5 { font-size: 1em; color: #34495e; }
        h6 { font-size: 0.9em; color: #34495e; }
        
        p {
          margin: 0.5em 0;
          text-align: justify;
          orphans: 2;
          widows: 2;
        }
        
        ul, ol {
          margin: 0.5em 0;
          padding-left: 2em;
        }
        
        li {
          margin: 0.25em 0;
        }
        
        blockquote {
          margin: 1em 0;
          padding: 0.5em 1em;
          border-left: 4px solid #3498db;
          background: #f8f9fa;
          font-style: italic;
        }
        
        code {
          background: #f1f2f6;
          padding: 0.2em 0.4em;
          border-radius: 3px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 0.9em;
        }
        
        pre {
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 4px;
          padding: 1em;
          margin: 1em 0;
          overflow-x: auto;
          page-break-inside: avoid;
        }
        
        pre code {
          background: none;
          padding: 0;
          border-radius: 0;
        }
        
        table {
          width: 100%;
          border-collapse: collapse;
          margin: 1em 0;
          page-break-inside: avoid;
        }
        
        th, td {
          border: 1px solid #dee2e6;
          padding: 0.5em;
          text-align: left;
        }
        
        th {
          background: #f8f9fa;
          font-weight: 600;
        }
        
        img {
          max-width: 100%;
          height: auto;
          margin: 1em 0;
          page-break-inside: avoid;
        }
        
        hr {
          border: none;
          border-top: 2px solid #e9ecef;
          margin: 2em 0;
        }
        
        a {
          color: #3498db;
          text-decoration: none;
        }
        
        a:hover {
          text-decoration: underline;
        }
        
        .page-break {
          page-break-before: always;
        }
        
        @media print {
          body {
            font-size: ${this.options.fontSize}px;
          }
          
          h1, h2, h3, h4, h5, h6 {
            page-break-after: avoid;
          }
          
          pre, blockquote, table, img {
            page-break-inside: avoid;
          }
          
          p, li {
            orphans: 2;
            widows: 2;
          }
        }
      </style>
    `;

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>${this.options.title}</title>
          ${styles}
        </head>
        <body>
          ${content}
        </body>
      </html>
    `;
  }

  private async renderHtmlToPdf(html: string): Promise<void> {
    // Create a temporary container for rendering
    const container = document.createElement('div');
    container.style.position = 'absolute';
    container.style.left = '-9999px';
    container.style.top = '-9999px';
    container.style.width = `${this.metrics.contentWidth * 3.78}px`; // Convert mm to px
    container.innerHTML = html;
    
    document.body.appendChild(container);

    try {
      // Configure html2canvas for optimal PDF rendering
      const canvas = await html2canvas(container, {
        scale: 2, // Higher scale for better quality
        useCORS: true,
        allowTaint: false,
        backgroundColor: '#ffffff',
        removeContainer: true,
        imageTimeout: 15000,
        logging: false,
        width: this.metrics.contentWidth * 3.78,
        windowWidth: this.metrics.contentWidth * 3.78,
        onclone: (clonedDoc) => {
          // Optimize cloned document for PDF rendering
          const clonedBody = clonedDoc.body;
          clonedBody.style.transform = 'scale(1)';
          clonedBody.style.transformOrigin = 'top left';
        }
      });

      // Calculate dimensions for PDF
      const imgWidth = this.metrics.contentWidth;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      
      // Add pages as needed
      let yPosition = 0;
      const pageHeight = this.metrics.contentHeight;
      
      while (yPosition < imgHeight) {
        if (yPosition > 0) {
          this.doc.addPage();
        }
        
        // Create canvas for this page
        const pageCanvas = document.createElement('canvas');
        const pageCtx = pageCanvas.getContext('2d')!;
        
        pageCanvas.width = canvas.width;
        pageCanvas.height = Math.min(
          (pageHeight * canvas.width) / imgWidth,
          canvas.height - (yPosition * canvas.width) / imgWidth
        );
        
        // Draw the portion of the image for this page
        pageCtx.drawImage(
          canvas,
          0, (yPosition * canvas.width) / imgWidth,
          canvas.width, pageCanvas.height,
          0, 0,
          canvas.width, pageCanvas.height
        );
        
        // Convert to optimized image data
        const imgData = pageCanvas.toDataURL('image/jpeg', this.options.quality);
        
        // Add image to PDF
        this.doc.addImage(
          imgData,
          'JPEG',
          this.metrics.margin.left,
          this.metrics.margin.top,
          imgWidth,
          Math.min(pageHeight, imgHeight - yPosition)
        );
        
        yPosition += pageHeight;
      }
      
    } finally {
      // Clean up
      document.body.removeChild(container);
    }
  }

  /**
   * Get PDF as blob for download
   */
  public getPdfBlob(): Blob {
    return this.doc.output('blob');
  }

  /**
   * Save PDF file
   */
  public save(filename: string): void {
    this.doc.save(filename);
  }
}

/**
 * Enhanced PDF export function with optimized settings
 */
export async function exportAsEnhancedPdf(
  markdown: string,
  filename: string,
  options: PdfExportOptions = {}
): Promise<void> {
  try {
    // Clean markdown content
    let cleanMarkdown = markdown;
    const filenamePattern = new RegExp(`^${filename.replace(/\.[^/.]+$/, '')}\\s*\\n?`, 'i');
    cleanMarkdown = cleanMarkdown.replace(filenamePattern, '');

    // Create exporter with optimized settings
    const exporter = new EnhancedPdfExporter({
      title: filename.replace(/\.[^/.]+$/, ''),
      author: 'Markdown Editor',
      quality: 0.85, // Balanced quality vs file size
      compression: true,
      fontSize: 11, // Slightly smaller for better page utilization
      lineHeight: 1.4,
      ...options
    });

    // Export to PDF
    await exporter.exportMarkdownToPdf(cleanMarkdown);

    // Save the file
    const pdfFilename = filename.replace(/\.[^/.]+$/, '.pdf');
    exporter.save(pdfFilename);

  } catch (error) {
    console.error('Enhanced PDF export failed:', error);
    throw new Error('Failed to export enhanced PDF');
  }
}
