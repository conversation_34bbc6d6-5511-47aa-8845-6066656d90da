/**
 * Session management hook for file cleanup and user activity tracking
 */

'use client';

import { useEffect, useRef, useCallback } from 'react';
import { scheduleFileCleanup, cancelFileCleanup } from '@/utils/export';

interface SessionConfig {
  inactivityTimeout?: number; // milliseconds
  fileCleanupDelay?: number; // milliseconds
  onSessionExpire?: () => void;
}

export function useSessionManagement(config: SessionConfig = {}) {
  const {
    inactivityTimeout = 1800000, // 30 minutes
    fileCleanupDelay = 300000, // 5 minutes
    onSessionExpire
  } = config;

  const activityTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastActivityRef = useRef<number>(Date.now());
  const activeFilesRef = useRef<Set<string>>(new Set());

  // Track user activity
  const updateActivity = useCallback(() => {
    lastActivityRef.current = Date.now();
    
    // Reset inactivity timer
    if (activityTimeoutRef.current) {
      clearTimeout(activityTimeoutRef.current);
    }
    
    activityTimeoutRef.current = setTimeout(() => {
      // User has been inactive, trigger session cleanup
      if (onSessionExpire) {
        onSessionExpire();
      }
      
      // Clean up any remaining files
      activeFilesRef.current.forEach(fileId => {
        cancelFileCleanup(fileId);
      });
      activeFilesRef.current.clear();
    }, inactivityTimeout);
  }, [inactivityTimeout, onSessionExpire]);

  // Register a file for cleanup
  const registerFile = useCallback((fileId: string, url: string) => {
    activeFilesRef.current.add(fileId);
    scheduleFileCleanup(fileId, url, fileCleanupDelay);
  }, [fileCleanupDelay]);

  // Unregister a file (user is still active with it)
  const unregisterFile = useCallback((fileId: string) => {
    activeFilesRef.current.delete(fileId);
    cancelFileCleanup(fileId);
  }, []);

  // Check if user is still active
  const isActive = useCallback(() => {
    return Date.now() - lastActivityRef.current < inactivityTimeout;
  }, [inactivityTimeout]);

  // Get session info
  const getSessionInfo = useCallback(() => {
    const now = Date.now();
    const timeSinceActivity = now - lastActivityRef.current;
    const timeUntilExpire = Math.max(0, inactivityTimeout - timeSinceActivity);
    
    return {
      isActive: isActive(),
      timeSinceActivity,
      timeUntilExpire,
      activeFileCount: activeFilesRef.current.size
    };
  }, [inactivityTimeout, isActive]);

  // Set up activity listeners
  useEffect(() => {
    const events = [
      'mousedown',
      'mousemove',
      'keypress',
      'scroll',
      'touchstart',
      'click',
      'focus'
    ];

    // Throttle activity updates to avoid excessive calls
    let throttleTimeout: NodeJS.Timeout | null = null;
    const throttledUpdateActivity = () => {
      if (throttleTimeout) return;
      
      throttleTimeout = setTimeout(() => {
        updateActivity();
        throttleTimeout = null;
      }, 1000); // Update at most once per second
    };

    events.forEach(event => {
      document.addEventListener(event, throttledUpdateActivity, true);
    });

    // Initial activity update
    updateActivity();

    // Cleanup
    return () => {
      events.forEach(event => {
        document.removeEventListener(event, throttledUpdateActivity, true);
      });
      
      if (activityTimeoutRef.current) {
        clearTimeout(activityTimeoutRef.current);
      }
      
      if (throttleTimeout) {
        clearTimeout(throttleTimeout);
      }
      
      // Clean up all registered files
      activeFilesRef.current.forEach(fileId => {
        cancelFileCleanup(fileId);
      });
    };
  }, [updateActivity]);

  // Handle page visibility changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        updateActivity();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [updateActivity]);

  // Handle beforeunload to clean up files
  useEffect(() => {
    const handleBeforeUnload = () => {
      // Clean up all files immediately when user leaves
      activeFilesRef.current.forEach(fileId => {
        cancelFileCleanup(fileId);
      });
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  return {
    registerFile,
    unregisterFile,
    isActive,
    getSessionInfo,
    updateActivity
  };
}

/**
 * Production session management with enhanced features
 */
export function useProductionSessionManagement() {
  return useSessionManagement({
    inactivityTimeout: 1800000, // 30 minutes
    fileCleanupDelay: 300000, // 5 minutes
    onSessionExpire: () => {
      // In production, you might want to:
      // - Save user work to localStorage
      // - Show a session expired notification
      // - Redirect to login if authenticated
      console.log('Session expired due to inactivity');
      
      // Save current work to localStorage as backup
      const currentContent = localStorage.getItem('mdeditor-current-content');
      if (currentContent) {
        const backup = {
          content: currentContent,
          timestamp: Date.now(),
          reason: 'session_expired'
        };
        localStorage.setItem('mdeditor-session-backup', JSON.stringify(backup));
      }
    }
  });
}
