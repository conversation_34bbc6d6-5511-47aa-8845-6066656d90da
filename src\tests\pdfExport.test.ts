/**
 * Comprehensive tests for PDF export functionality
 * Tests all export methods, quality settings, and optimization profiles
 */

import { describe, it, expect, beforeAll, afterAll } from '@jest/test-globals';
import { pdfExportService, UnifiedPdfOptions } from '../utils/unifiedPdfExport';
import { PdfOptimizationService } from '../utils/pdfOptimizationSettings';

// Test markdown content
const testMarkdown = `
# Test Document

This is a **test document** for PDF export validation.

## Features to Test

### Text Formatting
- **Bold text**
- *Italic text*
- \`inline code\`
- ~~strikethrough~~

### Lists
1. Numbered list item 1
2. Numbered list item 2
   - Nested bullet point
   - Another nested item

### Code Blocks
\`\`\`javascript
function testFunction() {
  console.log("This is a test");
  return true;
}
\`\`\`

### Tables
| Column 1 | Column 2 | Column 3 |
|----------|----------|----------|
| Cell 1   | Cell 2   | Cell 3   |
| Data A   | Data B   | Data C   |

### Blockquotes
> This is a blockquote
> with multiple lines
> to test formatting

### Links and Images
[Test Link](https://example.com)

---

## Mathematical Expressions
E = mc²

## Special Characters
Testing special characters: àáâãäåæçèéêë

## Long Paragraph
Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
`;

describe('PDF Export Service', () => {
  beforeAll(async () => {
    // Setup any required resources
  });

  afterAll(async () => {
    // Cleanup
    await pdfExportService.cleanup();
  });

  describe('Unified PDF Export Service', () => {
    it('should be available as singleton', () => {
      expect(pdfExportService).toBeDefined();
      expect(typeof pdfExportService.exportToPdf).toBe('function');
    });

    it('should list available methods', () => {
      const methods = pdfExportService.getAvailableMethods();
      expect(methods).toContain('puppeteer');
      expect(methods).toContain('playwright');
      expect(methods).toContain('markdown-pdf');
    });

    it('should provide method information', () => {
      const puppeteerInfo = pdfExportService.getMethodInfo('puppeteer');
      expect(puppeteerInfo.name).toBe('Puppeteer');
      expect(puppeteerInfo.description).toBeDefined();
      expect(puppeteerInfo.pros).toBeInstanceOf(Array);
      expect(puppeteerInfo.cons).toBeInstanceOf(Array);
    });
  });

  describe('PDF Optimization Profiles', () => {
    it('should have all required profiles', () => {
      const profiles = PdfOptimizationService.getAllProfiles();
      const profileNames = profiles.map(p => p.name);
      
      expect(profileNames).toContain('High Quality');
      expect(profileNames).toContain('Balanced');
      expect(profileNames).toContain('Compact');
      expect(profileNames).toContain('Print Ready');
      expect(profileNames).toContain('Mobile Optimized');
      expect(profileNames).toContain('Presentation');
    });

    it('should provide profile recommendations', () => {
      const professional = PdfOptimizationService.getRecommendedProfile('professional');
      expect(professional.name).toBe('High Quality');

      const web = PdfOptimizationService.getRecommendedProfile('web');
      expect(web.name).toBe('Compact');

      const print = PdfOptimizationService.getRecommendedProfile('print');
      expect(print.name).toBe('Print Ready');
    });

    it('should allow profile customization', () => {
      const customOptions = PdfOptimizationService.customizeProfile('balanced', {
        quality: 'high',
        format: 'Letter',
      });

      expect(customOptions.quality).toBe('high');
      expect(customOptions.format).toBe('Letter');
    });

    it('should estimate file sizes', () => {
      const estimate = PdfOptimizationService.estimateFileSize(1000, {
        quality: 'high',
        method: 'playwright',
        printBackground: true,
      });

      expect(estimate.min).toBeGreaterThan(0);
      expect(estimate.max).toBeGreaterThan(estimate.min);
      expect(estimate.unit).toBe('KB');
    });
  });

  describe('PDF Export Methods', () => {
    // Note: These tests may fail in CI environments without browser support
    // They should be run in development environments with proper browser setup

    it('should export with auto method selection', async () => {
      const options: UnifiedPdfOptions = {
        method: 'auto',
        quality: 'medium',
        title: 'Test Document',
      };

      const result = await pdfExportService.exportToPdf(testMarkdown, options);
      
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.method).toBeDefined();
      expect(result.fileSize).toBeGreaterThan(0);
      expect(result.generationTime).toBeGreaterThan(0);
    }, 30000); // 30 second timeout

    it('should handle export failures gracefully', async () => {
      const options: UnifiedPdfOptions = {
        method: 'puppeteer',
        timeout: 1, // Very short timeout to force failure
      };

      const result = await pdfExportService.exportToPdf(testMarkdown, options);
      
      // Should either succeed or fail gracefully
      if (!result.success) {
        expect(result.error).toBeDefined();
        expect(typeof result.error).toBe('string');
      }
    });

    it('should use fallback methods when enabled', async () => {
      const options: UnifiedPdfOptions = {
        method: 'markdown-pdf', // Try this first
        enableFallback: true,
        quality: 'low',
      };

      const result = await pdfExportService.exportToPdf(testMarkdown, options);
      
      // Should succeed with some method
      expect(result.success).toBe(true);
      expect(result.method).toBeDefined();
    }, 45000); // 45 second timeout for fallbacks
  });

  describe('PDF Quality and Size Optimization', () => {
    it('should produce different file sizes for different quality settings', async () => {
      const highQualityOptions: UnifiedPdfOptions = {
        method: 'auto',
        quality: 'high',
        enableFallback: false,
      };

      const lowQualityOptions: UnifiedPdfOptions = {
        method: 'auto',
        quality: 'low',
        enableFallback: false,
      };

      const highResult = await pdfExportService.exportToPdf(testMarkdown, highQualityOptions);
      const lowResult = await pdfExportService.exportToPdf(testMarkdown, lowQualityOptions);

      if (highResult.success && lowResult.success) {
        // High quality should generally produce larger files
        // Note: This may not always be true depending on content and compression
        expect(highResult.fileSize).toBeDefined();
        expect(lowResult.fileSize).toBeDefined();
      }
    }, 60000);

    it('should respect format and orientation settings', async () => {
      const portraitOptions: UnifiedPdfOptions = {
        method: 'auto',
        format: 'A4',
        orientation: 'portrait',
        enableFallback: false,
      };

      const landscapeOptions: UnifiedPdfOptions = {
        method: 'auto',
        format: 'A4',
        orientation: 'landscape',
        enableFallback: false,
      };

      const portraitResult = await pdfExportService.exportToPdf(testMarkdown, portraitOptions);
      const landscapeResult = await pdfExportService.exportToPdf(testMarkdown, landscapeOptions);

      expect(portraitResult.success || landscapeResult.success).toBe(true);
    }, 60000);
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle empty markdown', async () => {
      const options: UnifiedPdfOptions = {
        method: 'auto',
        enableFallback: true,
      };

      const result = await pdfExportService.exportToPdf('', options);
      
      // Should handle empty content gracefully
      expect(result).toBeDefined();
      expect(typeof result.success).toBe('boolean');
    });

    it('should handle very long content', async () => {
      const longMarkdown = '# Long Document\n\n' + 'Lorem ipsum dolor sit amet. '.repeat(1000);
      
      const options: UnifiedPdfOptions = {
        method: 'auto',
        quality: 'low', // Use low quality for speed
        enableFallback: true,
      };

      const result = await pdfExportService.exportToPdf(longMarkdown, options);
      
      if (result.success) {
        expect(result.fileSize).toBeGreaterThan(1000); // Should be substantial
      }
    }, 60000);

    it('should handle special characters and unicode', async () => {
      const unicodeMarkdown = `
# Unicode Test 🚀

## Emojis
😀 😃 😄 😁 😆 😅 😂 🤣

## Special Characters
àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ

## Mathematical Symbols
∑ ∏ ∫ ∂ ∇ ∞ ≈ ≠ ≤ ≥ ± × ÷

## Currency
$ € £ ¥ ₹ ₽ ₩ ₪
      `;

      const options: UnifiedPdfOptions = {
        method: 'auto',
        enableFallback: true,
      };

      const result = await pdfExportService.exportToPdf(unicodeMarkdown, options);
      
      // Should handle unicode content
      expect(result).toBeDefined();
    }, 30000);
  });
});

// Export test utilities for manual testing
export const testUtils = {
  testMarkdown,
  async manualTest(method: 'puppeteer' | 'playwright' | 'markdown-pdf') {
    console.log(`Testing ${method} PDF export...`);
    
    const options: UnifiedPdfOptions = {
      method,
      quality: 'medium',
      title: `Test Document - ${method}`,
      enableFallback: false,
    };

    const startTime = Date.now();
    const result = await pdfExportService.exportToPdf(testMarkdown, options);
    const endTime = Date.now();

    console.log(`${method} Result:`, {
      success: result.success,
      fileSize: result.fileSize ? `${(result.fileSize / 1024).toFixed(1)} KB` : 'N/A',
      generationTime: `${endTime - startTime}ms`,
      error: result.error,
    });

    return result;
  },

  async testAllMethods() {
    const methods: Array<'puppeteer' | 'playwright' | 'markdown-pdf'> = ['puppeteer', 'playwright', 'markdown-pdf'];
    const results = [];

    for (const method of methods) {
      try {
        const result = await this.manualTest(method);
        results.push({ method, result });
      } catch (error) {
        console.error(`${method} failed:`, error);
        results.push({ method, result: { success: false, error: error.message } });
      }
    }

    return results;
  }
};
