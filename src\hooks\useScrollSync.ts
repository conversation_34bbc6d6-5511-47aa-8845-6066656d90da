/**
 * Custom scroll synchronization hook
 * Better alternative to react-scroll-sync with more reliable performance
 */

import { useRef, useEffect, useCallback } from 'react';

interface ScrollSyncOptions {
  enabled?: boolean;
  throttleMs?: number;
}

export function useScrollSync(options: ScrollSyncOptions = {}) {
  const { enabled = true, throttleMs = 16 } = options;
  
  const scrollElements = useRef<Set<HTMLElement>>(new Set());
  const isScrolling = useRef<boolean>(false);
  const lastScrollTime = useRef<number>(0);
  const activeElement = useRef<HTMLElement | null>(null);

  // Throttle function
  const throttle = useCallback((func: Function, delay: number) => {
    return (...args: any[]) => {
      const now = Date.now();
      if (now - lastScrollTime.current >= delay) {
        lastScrollTime.current = now;
        func.apply(null, args);
      }
    };
  }, []);

  // Sync scroll position across all registered elements
  const syncScroll = useCallback(throttle((sourceElement: HTMLElement, scrollTop: number, scrollLeft: number) => {
    if (!enabled || isScrolling.current) return;
    
    isScrolling.current = true;
    activeElement.current = sourceElement;

    scrollElements.current.forEach(element => {
      if (element !== sourceElement) {
        // Calculate scroll percentage for better sync across different content heights
        const sourceScrollHeight = sourceElement.scrollHeight - sourceElement.clientHeight;
        const targetScrollHeight = element.scrollHeight - element.clientHeight;
        
        if (sourceScrollHeight > 0 && targetScrollHeight > 0) {
          const scrollPercentage = scrollTop / sourceScrollHeight;
          const targetScrollTop = scrollPercentage * targetScrollHeight;
          
          element.scrollTop = targetScrollTop;
          element.scrollLeft = scrollLeft;
        } else {
          // Fallback to direct sync if heights are similar
          element.scrollTop = scrollTop;
          element.scrollLeft = scrollLeft;
        }
      }
    });

    // Reset scrolling flag after a short delay
    setTimeout(() => {
      isScrolling.current = false;
      activeElement.current = null;
    }, 50);
  }, throttleMs), [enabled, throttleMs]);

  // Register a scroll element
  const registerElement = useCallback((element: HTMLElement | null) => {
    if (!element) return;

    // Add to set
    scrollElements.current.add(element);

    // Add scroll listener
    const handleScroll = (event: Event) => {
      const target = event.target as HTMLElement;
      if (target === activeElement.current) return; // Prevent feedback loops
      
      syncScroll(target, target.scrollTop, target.scrollLeft);
    };

    element.addEventListener('scroll', handleScroll, { passive: true });

    // Return cleanup function
    return () => {
      scrollElements.current.delete(element);
      element.removeEventListener('scroll', handleScroll);
    };
  }, [syncScroll]);

  // Unregister all elements on unmount
  useEffect(() => {
    return () => {
      scrollElements.current.clear();
    };
  }, []);

  return {
    registerElement,
    isScrolling: isScrolling.current,
    scrollElements: scrollElements.current
  };
}

// Hook for individual scroll panes
export function useScrollPane() {
  const elementRef = useRef<HTMLElement | null>(null);
  const cleanupRef = useRef<(() => void) | null>(null);

  const setRef = useCallback((element: HTMLElement | null, registerFn?: (el: HTMLElement | null) => (() => void) | undefined) => {
    // Cleanup previous element
    if (cleanupRef.current) {
      cleanupRef.current();
      cleanupRef.current = null;
    }

    elementRef.current = element;

    // Register new element
    if (element && registerFn) {
      cleanupRef.current = registerFn(element) || null;
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (cleanupRef.current) {
        cleanupRef.current();
      }
    };
  }, []);

  return {
    ref: elementRef,
    setRef
  };
}
