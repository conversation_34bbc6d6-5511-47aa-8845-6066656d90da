/**
 * Export utilities for different file formats
 */

import { ExportError } from '@/types';
import { markdownToHtml } from './markdownProcessor';
import { downloadFile } from './file';
import { exportToPDF } from './pdfExport';
import { exportAsEnhancedPdf } from './enhancedPdfExport';

// Session management for file cleanup
const activeFiles = new Map<string, { timeout: NodeJS.Timeout; url: string }>();

/**
 * Clean up temporary files after user session ends
 */
export function scheduleFileCleanup(fileId: string, url: string, delayMs: number = 300000) { // 5 minutes default
  // Clear existing timeout if any
  if (activeFiles.has(fileId)) {
    clearTimeout(activeFiles.get(fileId)!.timeout);
  }

  // Schedule cleanup
  const timeout = setTimeout(() => {
    try {
      URL.revokeObjectURL(url);
      activeFiles.delete(fileId);
    } catch (error) {
      console.warn('Failed to cleanup file:', error);
    }
  }, delayMs);

  activeFiles.set(fileId, { timeout, url });
}

/**
 * Cancel file cleanup (user is still active)
 */
export function cancelFileCleanup(fileId: string) {
  if (activeFiles.has(fileId)) {
    clearTimeout(activeFiles.get(fileId)!.timeout);
    activeFiles.delete(fileId);
  }
}



interface ExportOptions {
  format: 'md' | 'txt' | 'html' | 'pdf' | 'docx';
  pageSize?: string;
  margins?: { top: number; right: number; bottom: number; left: number };
  includeMetadata?: boolean;
}

/**
 * Export markdown as markdown file (.md)
 */
export async function exportAsMarkdown(
  markdown: string,
  filename: string
): Promise<void> {
  try {
    // Remove filename from content if it appears at the beginning
    let cleanMarkdown = markdown;
    const filenamePattern = new RegExp(`^${filename.replace(/\.[^/.]+$/, '')}\\s*\\n?`, 'i');
    cleanMarkdown = cleanMarkdown.replace(filenamePattern, '');

    const mdFilename = filename.replace(/\.[^/.]+$/, '.md');
    downloadFile(cleanMarkdown, mdFilename, 'text/markdown');

    // Schedule cleanup for the generated file
    const fileId = `md_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    scheduleFileCleanup(fileId, '', 300000); // 5 minutes
  } catch (error) {
    throw new ExportError('Failed to export as Markdown', 'md', error);
  }
}

/**
 * Export markdown as plain text
 */
export async function exportAsText(
  markdown: string,
  filename: string
): Promise<void> {
  try {
    // Remove filename from content if it appears at the beginning
    let cleanMarkdown = markdown;
    const filenamePattern = new RegExp(`^${filename.replace(/\.[^/.]+$/, '')}\\s*\\n?`, 'i');
    cleanMarkdown = cleanMarkdown.replace(filenamePattern, '');

    // Convert markdown to plain text with better formatting preservation
    const plainText = cleanMarkdown
      // Handle headers with proper formatting
      .replace(/^#{6}\s+(.+)$/gm, '      $1')
      .replace(/^#{5}\s+(.+)$/gm, '     $1')
      .replace(/^#{4}\s+(.+)$/gm, '    $1')
      .replace(/^#{3}\s+(.+)$/gm, '   $1')
      .replace(/^#{2}\s+(.+)$/gm, '  $1')
      .replace(/^#{1}\s+(.+)$/gm, ' $1')

      // Handle emphasis
      .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold markers
      .replace(/\*(.*?)\*/g, '$1') // Remove italic markers
      .replace(/~~(.*?)~~/g, '$1') // Remove strikethrough
      .replace(/==(.*?)==/g, '$1') // Remove marked text
      .replace(/\+\+(.*?)\+\+/g, '$1') // Remove inserted text

      // Handle code
      .replace(/`(.*?)`/g, '$1') // Remove inline code markers
      .replace(/```[\s\S]*?```/g, (match) => {
        // Preserve code blocks but remove fences
        return match.replace(/```[^\n]*\n?/g, '').replace(/```$/g, '');
      })

      // Handle links and images
      .replace(/!\[([^\]]*)\]\([^)]+\)/g, '[Image: $1]') // Images
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '$1 ($2)') // Links with URLs

      // Handle lists
      .replace(/^\s*[-*+]\s+/gm, '• ') // Convert bullet points
      .replace(/^\s*(\d+)\.\s+/gm, '$1. ') // Keep ordered list numbers

      // Handle blockquotes
      .replace(/^\s*>\s+/gm, '> ') // Keep blockquote markers

      // Handle horizontal rules
      .replace(/^[-*_]{3,}$/gm, '---')

      // Handle emojis (keep as text)
      .replace(/:([a-z_]+):/g, ':$1:')

      // Handle typographic replacements
      .replace(/\(c\)/gi, '©')
      .replace(/\(r\)/gi, '®')
      .replace(/\(tm\)/gi, '™')
      .replace(/\(p\)/gi, '℗')
      .replace(/\+-/g, '±')
      .replace(/\.{3}/g, '…')
      .replace(/--/g, '–')
      .replace(/---/g, '—')

      // Handle quotes
      .replace(/"([^"]+)"/g, '\u201c$1\u201d')
      .replace(/'([^']+)'/g, '\u2018$1\u2019')

      // Normalize line breaks
      .replace(/\n{3,}/g, '\n\n')
      .trim();

    const textFilename = filename.replace(/\.[^/.]+$/, '.txt');
    downloadFile(plainText, textFilename, 'text/plain');

    // Schedule cleanup for the generated file
    const fileId = `txt_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    scheduleFileCleanup(fileId, '', 300000); // 5 minutes
  } catch (error) {
    throw new ExportError('Failed to export as text', 'txt', error);
  }
}

/**
 * Export markdown as HTML
 */
export async function exportAsHtml(
  markdown: string,
  filename: string
): Promise<void> {
  try {
    const htmlContent = markdownToHtml(markdown);

    const fullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${filename}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.75;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            color: #374151;
            background: #ffffff;
        }

        h1, h2, h3, h4, h5, h6 {
            margin-top: 2rem;
            margin-bottom: 1rem;
            font-weight: 600;
            color: #111827;
        }

        h1 { font-size: 2.25rem; line-height: 2.5rem; }
        h2 { font-size: 1.875rem; line-height: 2.25rem; }
        h3 { font-size: 1.5rem; line-height: 2rem; }
        h4 { font-size: 1.25rem; line-height: 1.75rem; }
        h5 { font-size: 1.125rem; line-height: 1.75rem; }
        h6 { font-size: 1rem; line-height: 1.5rem; }

        p {
            margin-bottom: 1rem;
            line-height: 1.75;
        }

        /* Code styling to match preview */
        .code-block-wrapper {
            margin: 1rem 0;
            border-radius: 0.5rem;
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }

        .code-block-header {
            background-color: #f9fafb;
            padding: 0.5rem 1rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .code-language {
            font-size: 0.75rem;
            font-weight: 500;
            color: #6b7280;
            text-transform: uppercase;
        }

        .copy-code-btn {
            display: none; /* Hide in exports */
        }

        .code-block-wrapper pre {
            background-color: #f3f4f6;
            padding: 1rem;
            margin: 0;
            overflow-x: auto;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 0.875rem;
            line-height: 1.6;
        }

        .code-block-wrapper code {
            background: none;
            padding: 0;
            color: #374151;
        }

        /* Inline code */
        code:not(.code-block-wrapper code) {
            background-color: #f3f4f6;
            padding: 0.125rem 0.25rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            color: #374151;
        }

        /* Table styling to match preview */
        .table-wrapper {
            overflow-x: auto;
            margin: 1rem 0;
        }

        .markdown-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .markdown-table th,
        .markdown-table td {
            border: 1px solid #e5e7eb;
            padding: 0.5rem;
            text-align: left;
        }

        .markdown-table th {
            background-color: #f9fafb;
            font-weight: 600;
            color: #374151;
        }

        blockquote {
            border-left: 4px solid #e5e7eb;
            margin: 1rem 0;
            padding-left: 1rem;
            color: #6b7280;
            font-style: italic;
        }

        img {
            max-width: 100%;
            height: auto;
            border-radius: 0.5rem;
        }

        a {
            color: #3b82f6;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        ul, ol {
            margin: 1rem 0;
            padding-left: 2rem;
        }

        li {
            margin-bottom: 0.5rem;
        }

        hr {
            border: none;
            border-top: 1px solid #e5e7eb;
            margin: 2rem 0;
        }

        .anchor-link {
            opacity: 0;
            margin-left: 0.5rem;
            text-decoration: none;
            color: #6b7280;
        }

        h1:hover .anchor-link,
        h2:hover .anchor-link,
        h3:hover .anchor-link,
        h4:hover .anchor-link,
        h5:hover .anchor-link,
        h6:hover .anchor-link {
            opacity: 1;
        }

        @media print {
            body {
                margin: 0;
                padding: 1rem;
            }

            .anchor-link {
                display: none;
            }

            .copy-code-btn {
                display: none;
            }
        }
    </style>
</head>
<body>
    ${htmlContent}
</body>
</html>`;

    const htmlFilename = filename.replace(/\.[^/.]+$/, '.html');
    downloadFile(fullHtml, htmlFilename, 'text/html');

    // Schedule cleanup for the generated file
    const fileId = `html_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    scheduleFileCleanup(fileId, '', 300000); // 5 minutes
  } catch (error) {
    throw new ExportError('Failed to export as HTML', 'html', error);
  }
}

/**
 * Export markdown as PDF using enhanced PDF export with optimized file size
 */
export async function exportAsPdf(
  markdown: string,
  filename: string,
  options: Partial<ExportOptions> = {}
): Promise<void> {
  try {
    // Use the enhanced PDF exporter for better quality and smaller file size
    await exportAsEnhancedPdf(markdown, filename, {
      quality: 0.85, // Optimized for balance between quality and file size
      compression: true,
      fontSize: 11,
      lineHeight: 1.4,
      margin: options.margins || { top: 20, right: 20, bottom: 20, left: 20 },
      pageSize: options.pageSize === 'Letter' ? 'letter' :
                options.pageSize === 'Legal' ? 'legal' : 'a4'
    });

  } catch (error) {
    console.error('Error exporting enhanced PDF:', error);

    // Fallback to original PDF export if enhanced export fails
    try {
      console.log('Falling back to original PDF export...');

      let cleanMarkdown = markdown;
      const filenamePattern = new RegExp(`^${filename.replace(/\.[^/.]+$/, '')}\\s*\\n?`, 'i');
      cleanMarkdown = cleanMarkdown.replace(filenamePattern, '');

      const pdfData = exportToPDF(cleanMarkdown, {
        title: filename.replace(/\.[^/.]+$/, ''),
        author: 'Markdown Editor',
        fontSize: 12,
        lineHeight: 1.5,
        margin: options.margins || { top: 20, right: 20, bottom: 20, left: 20 }
      });

      // Create blob and download
      const blob = new Blob([pdfData], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);

      const pdfFilename = filename.replace(/\.[^/.]+$/, '.pdf');
      const link = document.createElement('a');
      link.href = url;
      link.download = pdfFilename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Schedule cleanup
      const fileId = `pdf_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      scheduleFileCleanup(fileId, url, 300000); // 5 minutes

    } catch (fallbackError) {
      console.error('Fallback PDF export also failed:', fallbackError);
      throw new ExportError('Failed to export as PDF', 'pdf', fallbackError);
    }
  }
}

/**
 * Export markdown as DOCX using @mohtasham/md-to-docx library
 */
export async function exportAsDocx(
  markdown: string,
  filename: string
): Promise<void> {
  try {
    // Dynamic import of the md-to-docx library
    const { convertMarkdownToDocx, downloadDocx } = await import('@mohtasham/md-to-docx');

    // Remove filename from content if it appears at the beginning
    let cleanMarkdown = markdown;
    const filenamePattern = new RegExp(`^${filename.replace(/\.[^/.]+$/, '')}\\s*\\n?`, 'i');
    cleanMarkdown = cleanMarkdown.replace(filenamePattern, '');

    // Configure export options for the md-to-docx library
    const docxOptions = {
      documentType: 'document' as const,
      style: {
        titleSize: 32,
        headingSpacing: 240,
        paragraphSpacing: 240,
        lineSpacing: 1.15,
        heading1Size: 32,
        heading2Size: 28,
        heading3Size: 24,
        heading4Size: 20,
        heading5Size: 18,
        paragraphSize: 24,
        listItemSize: 24,
        codeBlockSize: 20,
        blockquoteSize: 24,
        paragraphAlignment: 'LEFT' as const,
        blockquoteAlignment: 'LEFT' as const,
      }
    };

    // Convert markdown to DOCX blob
    const blob = await convertMarkdownToDocx(cleanMarkdown, docxOptions);

    // Download the file
    const docxFilename = filename.replace(/\.[^/.]+$/, '.docx');
    downloadDocx(blob, docxFilename);

  } catch (error) {
    console.error('DOCX Export Error:', error);
    throw new ExportError('Failed to export as DOCX', 'docx', error);
  }
}



export async function exportMarkdown(
  markdown: string,
  filename: string,
  options: ExportOptions
): Promise<void> {
  switch (options.format) {
    case 'md':
      return exportAsMarkdown(markdown, filename);
    case 'txt':
      return exportAsText(markdown, filename);
    case 'html':
      return exportAsHtml(markdown, filename);
    case 'pdf':
      return exportAsPdf(markdown, filename, options);
    case 'docx':
      return exportAsDocx(markdown, filename);
    default:
      throw new ExportError(`Unsupported export format: ${options.format}`, options.format);
  }
}

/**
 * Get available export formats
 */
export function getAvailableFormats(): Array<{
  value: string;
  label: string;
  description: string;
}> {
  return [
    {
      value: 'md',
      label: 'Markdown',
      description: 'Export as Markdown file (.md)'
    },
    {
      value: 'txt',
      label: 'Plain Text',
      description: 'Export as plain text file (.txt)'
    },
    {
      value: 'html',
      label: 'HTML',
      description: 'Export as HTML file (.html)'
    },
    {
      value: 'pdf',
      label: 'PDF',
      description: 'Export as PDF document (.pdf)'
    },
    {
      value: 'docx',
      label: 'Word Document',
      description: 'Export as Microsoft Word document (.docx)'
    }
  ];
}
