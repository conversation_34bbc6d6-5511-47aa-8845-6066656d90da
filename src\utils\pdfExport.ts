/**
 * Enhanced PDF export utility using jsPDF with better table support and text selection
 * Based on Context7 jsPDF documentation
 */

import { jsPDF } from 'jspdf';

interface ExportOptions {
  title?: string;
  author?: string;
  subject?: string;
  keywords?: string;
  fontSize?: number;
  lineHeight?: number;
  margin?: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
}

interface TableData {
  headers: string[];
  rows: string[][];
}

export class EnhancedPDFExporter {
  private doc: jsPDF;
  private options: Required<ExportOptions>;
  private currentY: number = 0;
  private pageHeight: number;
  private pageWidth: number;

  constructor(options: ExportOptions = {}) {
    this.options = {
      title: options.title || 'Markdown Document',
      author: options.author || 'Markdown Editor',
      subject: options.subject || 'Exported Document',
      keywords: options.keywords || 'markdown, export, pdf',
      fontSize: options.fontSize || 12,
      lineHeight: options.lineHeight || 1.5,
      margin: options.margin || { top: 20, right: 20, bottom: 20, left: 20 }
    };

    // Initialize jsPDF with better settings for text selection
    this.doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4',
      compress: true
    });

    this.pageHeight = this.doc.internal.pageSize.height;
    this.pageWidth = this.doc.internal.pageSize.width;
    this.currentY = this.options.margin.top;

    // Set document properties
    this.doc.setProperties({
      title: this.options.title,
      author: this.options.author,
      subject: this.options.subject,
      keywords: this.options.keywords,
      creator: 'Markdown Editor'
    });

    // Set default font for better text selection
    this.doc.setFont('helvetica', 'normal');
    this.doc.setFontSize(this.options.fontSize);
  }

  private checkPageBreak(height: number): void {
    if (this.currentY + height > this.pageHeight - this.options.margin.bottom) {
      this.doc.addPage();
      this.currentY = this.options.margin.top;
    }
  }

  private addText(text: string, fontSize?: number, fontStyle?: string): void {
    if (fontSize) this.doc.setFontSize(fontSize);
    if (fontStyle) this.doc.setFont('helvetica', fontStyle);

    const textWidth = this.pageWidth - this.options.margin.left - this.options.margin.right;
    const lines = this.doc.splitTextToSize(text, textWidth);
    
    const lineHeight = (fontSize || this.options.fontSize) * this.options.lineHeight * 0.352778; // Convert to mm
    
    this.checkPageBreak(lines.length * lineHeight);
    
    this.doc.text(lines, this.options.margin.left, this.currentY);
    this.currentY += lines.length * lineHeight + 2; // Add some spacing
    
    // Reset font
    this.doc.setFont('helvetica', 'normal');
    this.doc.setFontSize(this.options.fontSize);
  }

  private addHeading(text: string, level: number): void {
    const fontSize = Math.max(16 - level * 2, 10);
    const fontStyle = level <= 2 ? 'bold' : 'normal';
    
    // Add extra spacing before headings
    this.currentY += 5;
    this.addText(text, fontSize, fontStyle);
    this.currentY += 2;
  }

  private addTable(tableData: TableData): void {
    const cellPadding = 2;
    const rowHeight = 8;
    const colWidth = (this.pageWidth - this.options.margin.left - this.options.margin.right) / tableData.headers.length;
    
    // Check if table fits on current page
    const tableHeight = (tableData.rows.length + 1) * rowHeight;
    this.checkPageBreak(tableHeight);
    
    let startY = this.currentY;
    
    // Draw headers
    this.doc.setFont('helvetica', 'bold');
    this.doc.setFillColor(240, 240, 240);
    
    tableData.headers.forEach((header, index) => {
      const x = this.options.margin.left + index * colWidth;
      
      // Draw cell background
      this.doc.rect(x, startY, colWidth, rowHeight, 'F');
      
      // Draw cell border
      this.doc.rect(x, startY, colWidth, rowHeight, 'S');
      
      // Add text
      const textLines = this.doc.splitTextToSize(header, colWidth - cellPadding * 2);
      this.doc.text(textLines, x + cellPadding, startY + rowHeight / 2 + 1);
    });
    
    startY += rowHeight;
    
    // Draw data rows
    this.doc.setFont('helvetica', 'normal');
    this.doc.setFillColor(255, 255, 255);
    
    tableData.rows.forEach((row, rowIndex) => {
      row.forEach((cell, colIndex) => {
        const x = this.options.margin.left + colIndex * colWidth;
        const y = startY + rowIndex * rowHeight;
        
        // Draw cell background (alternating colors)
        if (rowIndex % 2 === 1) {
          this.doc.setFillColor(248, 248, 248);
          this.doc.rect(x, y, colWidth, rowHeight, 'F');
        }
        
        // Draw cell border
        this.doc.rect(x, y, colWidth, rowHeight, 'S');
        
        // Add text
        const textLines = this.doc.splitTextToSize(cell, colWidth - cellPadding * 2);
        this.doc.text(textLines, x + cellPadding, y + rowHeight / 2 + 1);
      });
    });
    
    this.currentY = startY + tableData.rows.length * rowHeight + 5;
  }

  private addCodeBlock(code: string, language?: string): void {
    // Add language label if provided
    if (language) {
      this.doc.setFont('helvetica', 'bold');
      this.doc.setFontSize(10);
      this.addText(`Code (${language}):`);
    }
    
    // Set monospace font for code
    this.doc.setFont('courier', 'normal');
    this.doc.setFontSize(10);
    
    // Add background color
    const textWidth = this.pageWidth - this.options.margin.left - this.options.margin.right;
    const lines = this.doc.splitTextToSize(code, textWidth - 4);
    const lineHeight = 10 * 0.352778;
    const blockHeight = lines.length * lineHeight + 4;
    
    this.checkPageBreak(blockHeight);
    
    // Draw background
    this.doc.setFillColor(245, 245, 245);
    this.doc.rect(this.options.margin.left, this.currentY - 2, textWidth, blockHeight, 'F');
    
    // Add text
    this.doc.text(lines, this.options.margin.left + 2, this.currentY + 2);
    this.currentY += blockHeight + 2;
    
    // Reset font
    this.doc.setFont('helvetica', 'normal');
    this.doc.setFontSize(this.options.fontSize);
  }

  public exportMarkdownToPDF(markdownContent: string): Uint8Array {
    const lines = markdownContent.split('\n');
    let inCodeBlock = false;
    let codeBlockContent = '';
    let codeBlockLanguage = '';
    let currentTable: TableData | null = null;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // Handle code blocks
      if (line.startsWith('```')) {
        if (inCodeBlock) {
          // End of code block
          this.addCodeBlock(codeBlockContent.trim(), codeBlockLanguage);
          inCodeBlock = false;
          codeBlockContent = '';
          codeBlockLanguage = '';
        } else {
          // Start of code block
          inCodeBlock = true;
          codeBlockLanguage = line.substring(3).trim();
        }
        continue;
      }
      
      if (inCodeBlock) {
        codeBlockContent += line + '\n';
        continue;
      }
      
      // Handle tables
      if (line.includes('|') && line.trim().length > 0) {
        const cells = line.split('|').map(cell => cell.trim()).filter(cell => cell.length > 0);
        
        if (!currentTable) {
          currentTable = { headers: cells, rows: [] };
        } else if (line.includes('---')) {
          // Skip separator line
          continue;
        } else {
          currentTable.rows.push(cells);
        }
        continue;
      } else if (currentTable) {
        // End of table
        this.addTable(currentTable);
        currentTable = null;
      }
      
      // Handle headings
      if (line.startsWith('#')) {
        const level = line.match(/^#+/)?.[0].length || 1;
        const text = line.substring(level).trim();
        this.addHeading(text, level);
        continue;
      }
      
      // Handle regular text
      if (line.trim().length > 0) {
        // Process inline formatting
        let processedLine = line
          .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold markers
          .replace(/\*(.*?)\*/g, '$1')     // Remove italic markers
          .replace(/`(.*?)`/g, '$1')       // Remove inline code markers
          .replace(/~~(.*?)~~/g, '$1');    // Remove strikethrough markers
        
        this.addText(processedLine);
      } else {
        // Empty line - add spacing
        this.currentY += 3;
      }
    }
    
    // Handle any remaining table
    if (currentTable) {
      this.addTable(currentTable);
    }
    
    return this.doc.output('arraybuffer') as Uint8Array;
  }

  public save(filename: string = 'document.pdf'): void {
    this.doc.save(filename);
  }
}

export function exportToPDF(content: string, options: ExportOptions = {}): Uint8Array {
  const exporter = new EnhancedPDFExporter(options);
  return exporter.exportMarkdownToPDF(content);
}
