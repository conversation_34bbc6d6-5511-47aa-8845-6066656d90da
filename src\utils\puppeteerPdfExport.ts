/**
 * Production-grade PDF export using Puppeteer
 * Generates high-quality, text-selectable PDFs with optimized file sizes
 */

import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON>, PDFOptions } from 'puppeteer';
import { processMarkdown } from './markdownProcessor';

export interface PuppeteerPdfOptions {
  title?: string;
  author?: string;
  subject?: string;
  keywords?: string;
  format?: 'A4' | 'A3' | 'A5' | 'Letter' | 'Legal' | 'Tabloid';
  orientation?: 'portrait' | 'landscape';
  margin?: {
    top?: string;
    right?: string;
    bottom?: string;
    left?: string;
  };
  displayHeaderFooter?: boolean;
  headerTemplate?: string;
  footerTemplate?: string;
  printBackground?: boolean;
  scale?: number;
  preferCSSPageSize?: boolean;
  quality?: 'high' | 'medium' | 'low';
  timeout?: number;
}

export class PuppeteerPdfExporter {
  private browser: Browser | null = null;
  private options: Required<PuppeteerPdfOptions>;

  constructor(options: PuppeteerPdfOptions = {}) {
    this.options = {
      title: options.title || 'Markdown Document',
      author: options.author || 'Markdown Editor',
      subject: options.subject || 'Exported Document',
      keywords: options.keywords || 'markdown, export, pdf',
      format: options.format || 'A4',
      orientation: options.orientation || 'portrait',
      margin: {
        top: options.margin?.top || '1in',
        right: options.margin?.right || '1in',
        bottom: options.margin?.bottom || '1in',
        left: options.margin?.left || '1in',
      },
      displayHeaderFooter: options.displayHeaderFooter || false,
      headerTemplate: options.headerTemplate || '',
      footerTemplate: options.footerTemplate || '',
      printBackground: options.printBackground || true,
      scale: options.scale || 1,
      preferCSSPageSize: options.preferCSSPageSize || false,
      quality: options.quality || 'high',
      timeout: options.timeout || 30000,
    };
  }

  private async initializeBrowser(): Promise<void> {
    if (!this.browser) {
      this.browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--single-process',
          '--disable-gpu',
        ],
      });
    }
  }

  private createOptimizedHtml(content: string): string {
    const styles = `
      <style>
        @page {
          size: ${this.options.format};
          margin: ${this.options.margin.top} ${this.options.margin.right} ${this.options.margin.bottom} ${this.options.margin.left};
        }
        
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
          font-size: 12px;
          line-height: 1.6;
          color: #333;
          background: white;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        }
        
        h1, h2, h3, h4, h5, h6 {
          margin: 1.5em 0 0.5em 0;
          font-weight: 600;
          line-height: 1.2;
          page-break-after: avoid;
          color: #2c3e50;
        }
        
        h1 { font-size: 2em; border-bottom: 2px solid #3498db; padding-bottom: 0.3em; }
        h2 { font-size: 1.5em; border-bottom: 1px solid #bdc3c7; padding-bottom: 0.2em; }
        h3 { font-size: 1.25em; }
        h4 { font-size: 1.1em; }
        h5 { font-size: 1em; font-weight: 700; }
        h6 { font-size: 0.9em; font-weight: 700; color: #7f8c8d; }
        
        p {
          margin: 0.8em 0;
          orphans: 2;
          widows: 2;
        }
        
        blockquote {
          margin: 1em 0;
          padding: 0.5em 1em;
          border-left: 4px solid #3498db;
          background: #f8f9fa;
          font-style: italic;
          page-break-inside: avoid;
        }
        
        pre {
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 4px;
          padding: 1em;
          margin: 1em 0;
          overflow-x: auto;
          page-break-inside: avoid;
          font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
          font-size: 0.9em;
        }
        
        code {
          background: #f1f3f4;
          padding: 0.2em 0.4em;
          border-radius: 3px;
          font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
          font-size: 0.9em;
        }
        
        pre code {
          background: none;
          padding: 0;
        }
        
        table {
          width: 100%;
          border-collapse: collapse;
          margin: 1em 0;
          page-break-inside: avoid;
        }
        
        th, td {
          border: 1px solid #ddd;
          padding: 0.5em;
          text-align: left;
        }
        
        th {
          background: #f8f9fa;
          font-weight: 600;
        }
        
        ul, ol {
          margin: 0.8em 0;
          padding-left: 2em;
        }
        
        li {
          margin: 0.3em 0;
        }
        
        img {
          max-width: 100%;
          height: auto;
          page-break-inside: avoid;
        }
        
        hr {
          border: none;
          border-top: 2px solid #e9ecef;
          margin: 2em 0;
        }
        
        a {
          color: #3498db;
          text-decoration: none;
        }
        
        a:hover {
          text-decoration: underline;
        }
        
        .page-break {
          page-break-before: always;
        }
        
        @media print {
          body {
            font-size: 12px;
          }
          
          h1, h2, h3, h4, h5, h6 {
            page-break-after: avoid;
          }
          
          pre, blockquote, table, img {
            page-break-inside: avoid;
          }
          
          p, li {
            orphans: 2;
            widows: 2;
          }
          
          a[href]:after {
            content: " (" attr(href) ")";
            font-size: 0.8em;
            color: #666;
          }
        }
      </style>
    `;

    return `
      <!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <title>${this.options.title}</title>
          <meta name="author" content="${this.options.author}">
          <meta name="subject" content="${this.options.subject}">
          <meta name="keywords" content="${this.options.keywords}">
          ${styles}
        </head>
        <body>
          ${content}
        </body>
      </html>
    `;
  }

  private getPdfOptions(): PDFOptions {
    const baseOptions: PDFOptions = {
      format: this.options.format,
      landscape: this.options.orientation === 'landscape',
      printBackground: this.options.printBackground,
      margin: this.options.margin,
      displayHeaderFooter: this.options.displayHeaderFooter,
      headerTemplate: this.options.headerTemplate,
      footerTemplate: this.options.footerTemplate,
      scale: this.options.scale,
      preferCSSPageSize: this.options.preferCSSPageSize,
      timeout: this.options.timeout,
    };

    // Adjust quality settings
    if (this.options.quality === 'high') {
      return {
        ...baseOptions,
        scale: 1,
        printBackground: true,
      };
    } else if (this.options.quality === 'medium') {
      return {
        ...baseOptions,
        scale: 0.9,
        printBackground: true,
      };
    } else {
      return {
        ...baseOptions,
        scale: 0.8,
        printBackground: false,
      };
    }
  }

  public async exportMarkdownToPdf(markdown: string): Promise<Uint8Array> {
    try {
      await this.initializeBrowser();
      
      if (!this.browser) {
        throw new Error('Failed to initialize browser');
      }

      const page: Page = await this.browser.newPage();
      
      try {
        // Process markdown to HTML
        const htmlContent = processMarkdown(markdown);
        
        // Create optimized HTML
        const optimizedHtml = this.createOptimizedHtml(htmlContent);
        
        // Set content and wait for it to load
        await page.setContent(optimizedHtml, {
          waitUntil: 'networkidle0',
          timeout: this.options.timeout,
        });
        
        // Generate PDF
        const pdfBuffer = await page.pdf(this.getPdfOptions());
        
        return new Uint8Array(pdfBuffer);
        
      } finally {
        await page.close();
      }
      
    } catch (error) {
      console.error('Error generating PDF with Puppeteer:', error);
      throw new Error(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  public async close(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }

  public getPdfBlob(pdfData: Uint8Array): Blob {
    return new Blob([pdfData], { type: 'application/pdf' });
  }

  public downloadPdf(pdfData: Uint8Array, filename: string): void {
    const blob = this.getPdfBlob(pdfData);
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = filename.replace(/\.[^/.]+$/, '.pdf');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Clean up
    setTimeout(() => URL.revokeObjectURL(url), 1000);
  }
}

/**
 * Convenience function for quick PDF export
 */
export async function exportMarkdownToPdfWithPuppeteer(
  markdown: string,
  filename: string,
  options: PuppeteerPdfOptions = {}
): Promise<void> {
  const exporter = new PuppeteerPdfExporter(options);
  
  try {
    const pdfData = await exporter.exportMarkdownToPdf(markdown);
    exporter.downloadPdf(pdfData, filename);
  } finally {
    await exporter.close();
  }
}
