/**
 * Enhanced Mobile Interface with Modern UX Design
 * Optimized for touch interactions and mobile-first experience
 */

'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useApp } from '@/contexts/AppContext';
import { useResponsive } from '@/hooks/useResponsive';
import { EditorPane } from '../Editor/EditorPane';
import { PreviewPane } from '../Preview/PreviewPane';

interface EnhancedMobileInterfaceProps {
  className?: string;
}

type ViewMode = 'edit' | 'preview' | 'split';
type PanelState = 'collapsed' | 'partial' | 'expanded';

export function EnhancedMobileInterface({ className = '' }: EnhancedMobileInterfaceProps) {
  const { state, updateContent } = useApp();
  const responsive = useResponsive();
  
  // View state
  const [viewMode, setViewMode] = useState<ViewMode>('edit');
  const [panelState, setPanelState] = useState<PanelState>('expanded');
  const [showQuickActions, setShowQuickActions] = useState(false);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  
  // Touch and gesture handling
  const [touchStart, setTouchStart] = useState<{ x: number; y: number } | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Keyboard visibility detection
  useEffect(() => {
    const handleResize = () => {
      const viewportHeight = window.visualViewport?.height || window.innerHeight;
      const windowHeight = window.innerHeight;
      setIsKeyboardVisible(viewportHeight < windowHeight * 0.75);
    };

    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', handleResize);
      return () => window.visualViewport.removeEventListener('resize', handleResize);
    } else {
      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  }, []);

  // Touch gesture handlers
  const handleTouchStart = (e: React.TouchEvent) => {
    const touch = e.touches[0];
    setTouchStart({ x: touch.clientX, y: touch.clientY });
    setIsDragging(false);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!touchStart) return;
    
    const touch = e.touches[0];
    const deltaX = touch.clientX - touchStart.x;
    const deltaY = touch.clientY - touchStart.y;
    
    // Detect horizontal swipe for view switching
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
      setIsDragging(true);
      
      if (deltaX > 100 && viewMode === 'preview') {
        setViewMode('edit');
      } else if (deltaX < -100 && viewMode === 'edit') {
        setViewMode('preview');
      }
    }
    
    // Detect vertical swipe for panel state
    if (Math.abs(deltaY) > Math.abs(deltaX) && Math.abs(deltaY) > 50) {
      if (deltaY > 100 && panelState === 'expanded') {
        setPanelState('partial');
      } else if (deltaY < -100 && panelState === 'partial') {
        setPanelState('expanded');
      }
    }
  };

  const handleTouchEnd = () => {
    setTouchStart(null);
    setIsDragging(false);
  };

  // Quick actions for common markdown operations
  const quickActions = [
    { icon: '**', label: 'Bold', action: () => insertMarkdown('**', '**') },
    { icon: '*', label: 'Italic', action: () => insertMarkdown('*', '*') },
    { icon: '#', label: 'Header', action: () => insertMarkdown('# ', '') },
    { icon: '[]', label: 'Link', action: () => insertMarkdown('[', '](url)') },
    { icon: '`', label: 'Code', action: () => insertMarkdown('`', '`') },
    { icon: '•', label: 'List', action: () => insertMarkdown('- ', '') },
  ];

  const insertMarkdown = (before: string, after: string) => {
    // Implementation would depend on your editor setup
    console.log('Insert markdown:', before, after);
  };

  return (
    <div 
      ref={containerRef}
      className={`h-full flex flex-col bg-gray-50 dark:bg-gray-900 ${className}`}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* Enhanced Header with Gesture Indicators */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        {/* Status Bar */}
        <div className="h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500" />
        
        {/* Navigation Bar */}
        <div className="flex items-center justify-between px-4 py-3">
          <div className="flex items-center gap-3">
            <button
              onClick={() => setShowQuickActions(!showQuickActions)}
              className="p-2 rounded-full bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
            
            <div className="text-sm font-medium text-gray-900 dark:text-white">
              {state.editor.currentFile?.name || 'Untitled'}
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {/* View Mode Selector */}
            <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
              <button
                onClick={() => setViewMode('edit')}
                className={`px-3 py-1 rounded-md text-xs font-medium transition-all ${
                  viewMode === 'edit'
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-400'
                }`}
              >
                Edit
              </button>
              <button
                onClick={() => setViewMode('preview')}
                className={`px-3 py-1 rounded-md text-xs font-medium transition-all ${
                  viewMode === 'preview'
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-400'
                }`}
              >
                Preview
              </button>
            </div>
          </div>
        </div>
        
        {/* Swipe Indicator */}
        <div className="flex justify-center pb-2">
          <div className="flex gap-1">
            <div className={`w-2 h-1 rounded-full transition-colors ${
              viewMode === 'edit' ? 'bg-blue-500' : 'bg-gray-300 dark:bg-gray-600'
            }`} />
            <div className={`w-2 h-1 rounded-full transition-colors ${
              viewMode === 'preview' ? 'bg-blue-500' : 'bg-gray-300 dark:bg-gray-600'
            }`} />
          </div>
        </div>
      </div>

      {/* Quick Actions Panel */}
      {showQuickActions && (
        <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
          <div className="flex gap-2 overflow-x-auto">
            {quickActions.map((action, index) => (
              <button
                key={index}
                onClick={action.action}
                className="flex-shrink-0 flex flex-col items-center gap-1 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors min-w-[60px]"
              >
                <span className="text-lg font-mono">{action.icon}</span>
                <span className="text-xs text-gray-600 dark:text-gray-400">{action.label}</span>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Main Content Area */}
      <div className="flex-1 relative overflow-hidden">
        {/* Content Container with Smooth Transitions */}
        <div 
          className={`h-full transition-transform duration-300 ease-out ${
            isDragging ? 'transition-none' : ''
          }`}
          style={{
            transform: viewMode === 'edit' ? 'translateX(0%)' : 
                      viewMode === 'preview' ? 'translateX(-100%)' : 'translateX(-50%)'
          }}
        >
          <div className="flex h-full w-[200%]">
            {/* Editor Panel */}
            <div className="w-1/2 h-full">
              <EditorPane 
                scrollRegister={() => {}}
                className="h-full"
              />
            </div>
            
            {/* Preview Panel */}
            <div className="w-1/2 h-full">
              <PreviewPane 
                scrollRegister={() => {}}
                className="h-full"
              />
            </div>
          </div>
        </div>
        
        {/* Floating Action Button */}
        {!isKeyboardVisible && (
          <button
            onClick={() => setViewMode(viewMode === 'edit' ? 'preview' : 'edit')}
            className="absolute bottom-6 right-6 w-14 h-14 bg-blue-500 hover:bg-blue-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center z-10"
          >
            <svg 
              className={`w-6 h-6 transition-transform duration-200 ${
                viewMode === 'edit' ? 'rotate-0' : 'rotate-180'
              }`} 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
          </button>
        )}
      </div>

      {/* Bottom Safe Area for iOS */}
      <div className="h-safe-bottom bg-white dark:bg-gray-800" />
      
      {/* Touch Feedback Overlay */}
      {isDragging && (
        <div className="absolute inset-0 bg-black bg-opacity-10 pointer-events-none z-20" />
      )}
    </div>
  );
}
