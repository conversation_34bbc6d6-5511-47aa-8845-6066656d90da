/**
 * Client-side PDF export service
 * Calls the server-side PDF generation API
 */

export interface ClientPdfOptions {
  method?: 'puppeteer' | 'playwright' | 'markdown-pdf' | 'auto';
  title?: string;
  author?: string;
  quality?: 'high' | 'medium' | 'low';
  format?: 'A4' | 'A3' | 'A5' | 'Letter' | 'Legal' | 'Tabloid';
  orientation?: 'portrait' | 'landscape';
  margin?: {
    top?: string;
    right?: string;
    bottom?: string;
    left?: string;
  };
}

export interface PdfExportResult {
  success: boolean;
  error?: string;
  method?: string;
  fileSize?: number;
  generationTime?: number;
}

export class ClientPdfExportService {
  private static instance: ClientPdfExportService;

  private constructor() {}

  public static getInstance(): ClientPdfExportService {
    if (!ClientPdfExportService.instance) {
      ClientPdfExportService.instance = new ClientPdfExportService();
    }
    return ClientPdfExportService.instance;
  }

  public async exportToPdf(
    markdown: string,
    filename: string,
    options: ClientPdfOptions = {}
  ): Promise<PdfExportResult> {
    const startTime = Date.now();

    try {
      const response = await fetch('/api/export/pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          markdown,
          filename,
          method: options.method || 'auto',
          options,
        }),
      });

      const generationTime = Date.now() - startTime;

      if (!response.ok) {
        const errorData = await response.json();
        return {
          success: false,
          error: errorData.error || `HTTP ${response.status}`,
          generationTime,
        };
      }

      // Get metadata from headers
      const method = response.headers.get('X-PDF-Method') || 'unknown';
      const fileSize = parseInt(response.headers.get('X-PDF-Size') || '0');

      // Download the PDF
      const blob = await response.blob();
      this.downloadBlob(blob, filename.replace(/\.[^/.]+$/, '.pdf'));

      return {
        success: true,
        method,
        fileSize,
        generationTime,
      };

    } catch (error) {
      const generationTime = Date.now() - startTime;
      console.error('PDF export failed:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
        generationTime,
      };
    }
  }

  private downloadBlob(blob: Blob, filename: string): void {
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Clean up
    setTimeout(() => URL.revokeObjectURL(url), 1000);
  }

  public async checkHealth(): Promise<{ status: string; availableMethods: string[] }> {
    try {
      const response = await fetch('/api/export/pdf');
      return await response.json();
    } catch (error) {
      console.error('Health check failed:', error);
      return {
        status: 'error',
        availableMethods: [],
      };
    }
  }

  public getAvailableMethods(): string[] {
    return ['puppeteer', 'playwright', 'markdown-pdf', 'auto'];
  }

  public getMethodInfo(method: string): { name: string; description: string; pros: string[]; cons: string[] } {
    const methodInfo = {
      puppeteer: {
        name: 'Puppeteer',
        description: 'High-quality PDF generation using Chrome/Chromium',
        pros: [
          'Excellent rendering quality',
          'Full CSS support',
          'Text selectable',
          'Good performance',
          'Reliable'
        ],
        cons: [
          'Requires server-side processing',
          'Higher memory usage',
          'Slower for simple documents'
        ]
      },
      playwright: {
        name: 'Playwright',
        description: 'Premium PDF generation with accessibility features',
        pros: [
          'Best rendering quality',
          'Accessibility support (tagged PDFs)',
          'Document outline support',
          'Text selectable',
          'Cross-browser support'
        ],
        cons: [
          'Requires server-side processing',
          'Highest memory usage',
          'Slower startup'
        ]
      },
      'markdown-pdf': {
        name: 'Markdown-PDF',
        description: 'Direct markdown to PDF conversion',
        pros: [
          'Lightweight',
          'Fast processing',
          'Good for simple documents',
          'Text selectable'
        ],
        cons: [
          'Limited CSS support',
          'Basic styling options',
          'May have rendering issues with complex content'
        ]
      },
      auto: {
        name: 'Auto Selection',
        description: 'Automatically selects the best method based on requirements',
        pros: [
          'Optimized for each use case',
          'Fallback support',
          'Best balance of quality and performance'
        ],
        cons: [
          'Less predictable method selection',
          'May vary between exports'
        ]
      }
    };

    return methodInfo[method] || {
      name: 'Unknown',
      description: 'Unknown method',
      pros: [],
      cons: []
    };
  }
}

// Export singleton instance
export const clientPdfExportService = ClientPdfExportService.getInstance();

// Convenience function
export async function exportMarkdownToPdf(
  markdown: string,
  filename: string,
  options: ClientPdfOptions = {}
): Promise<PdfExportResult> {
  return clientPdfExportService.exportToPdf(markdown, filename, options);
}
