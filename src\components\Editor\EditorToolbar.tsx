/**
 * Editor toolbar with formatting buttons
 */

'use client';

import React, { useState } from 'react';
import { ConfirmDialog } from '@/components/UI/ConfirmDialog';

interface EditorToolbarProps {
  onInsertMarkdown: (before: string, after: string, placeholder: string) => void;
  onUndo: () => void;
  onRedo: () => void;
  onClear: () => void;
  onPaste: () => void;
  canUndo: boolean;
  canRedo: boolean;
}

export function EditorToolbar({
  onInsertMarkdown,
  onUndo,
  onRedo,
  onClear,
  onPaste,
  canUndo,
  canRedo
}: EditorToolbarProps) {

  const [showTableDialog, setShowTableDialog] = useState(false);
  const [showClearDialog, setShowClearDialog] = useState(false);
  const [tableRows, setTableRows] = useState(3);
  const [tableCols, setTableCols] = useState(3);

  const toolbarButtons = [
    {
      group: 'actions',
      buttons: [
        {
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
            </svg>
          ),
          title: 'Undo (Ctrl+Z)',
          action: onUndo,
          disabled: !canUndo
        },
        {
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 10h-10a8 8 0 00-8 8v2M21 10l-6 6m6-6l-6-6" />
            </svg>
          ),
          title: 'Redo (Ctrl+Y)',
          action: onRedo,
          disabled: !canRedo
        },
        {
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          ),
          title: 'Paste (Ctrl+Shift+V)',
          action: onPaste
        },
        {
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          ),
          title: 'Clear All',
          action: () => setShowClearDialog(true),
          className: 'text-red-600 hover:text-red-700'
        }
      ]
    },

    {
      group: 'structure',
      buttons: [
        {
          icon: '#',
          title: 'Heading',
          action: () => onInsertMarkdown('# ', '', 'Heading'),
          className: 'font-bold text-lg'
        },
        {
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
            </svg>
          ),
          title: 'Unordered List',
          action: () => insertUnorderedList()
        },
        {
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          ),
          title: 'Ordered List',
          action: () => insertOrderedList()
        },
        {
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          ),
          title: 'Blockquote',
          action: () => onInsertMarkdown('> ', '', 'blockquote text')
        }
      ]
    },
    {
      group: 'media',
      buttons: [
        {
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
            </svg>
          ),
          title: 'Link (Ctrl+K)',
          action: () => onInsertMarkdown('[', '](url)', 'link text')
        },
        {
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          ),
          title: 'Image',
          action: () => onInsertMarkdown('![', '](image-url)', 'alt text')
        },
        {
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
          ),
          title: 'Table',
          action: () => setShowTableDialog(true)
        }
      ]
    },
    {
      group: 'code',
      buttons: [
        {
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
            </svg>
          ),
          title: 'Code Block',
          action: () => onInsertMarkdown('```\n', '\n```', 'code here')
        },
        {
          icon: '---',
          title: 'Horizontal Rule',
          action: () => onInsertMarkdown('\n---\n', '', ''),
          className: 'text-xs'
        }
      ]
    }
  ];



  const insertUnorderedList = () => {
    // Check if we're at the start of a line or need a new line
    onInsertMarkdown('\n- ', '', 'list item\n- another item\n- third item');
  };

  const insertOrderedList = () => {
    // Check if we're at the start of a line or need a new line
    onInsertMarkdown('\n1. ', '', 'first item\n2. second item\n3. third item');
  };

  const insertTable = () => {
    const headers = Array(tableCols).fill('Header').map((h, i) => `${h} ${i + 1}`).join(' | ');
    const separator = Array(tableCols).fill('---').join(' | ');
    const rows = Array(tableRows - 1).fill(null).map((_, rowIndex) =>
      Array(tableCols).fill('Cell').map((c, colIndex) => `${c} ${rowIndex + 1}-${colIndex + 1}`).join(' | ')
    ).join('\n');

    const table = `| ${headers} |\n| ${separator} |\n| ${rows} |`;
    onInsertMarkdown('\n', '\n', table);
    setShowTableDialog(false);
  };

  return (
    <div className="border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 px-1 py-1 min-h-[40px]">
      <div className="flex items-center space-x-0.5 overflow-x-auto scrollbar-none">
        {toolbarButtons.map((group, groupIndex) => (
          <React.Fragment key={group.group}>
            {groupIndex > 0 && (
              <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2"></div>
            )}

            {group.buttons.map((button, buttonIndex) => (
              <div key={buttonIndex} className="relative">
                <button
                  onClick={button.action}
                  disabled={'disabled' in button ? button.disabled : false}
                  className={`p-1 rounded transition-colors text-xs min-w-[28px] h-7 flex items-center justify-center ${
                    ('disabled' in button && button.disabled)
                      ? 'opacity-50 cursor-not-allowed'
                      : 'hover:bg-gray-200 dark:hover:bg-gray-700'
                  } ${'className' in button ? button.className || '' : ''}`}
                  title={button.title}
                >
                  {typeof button.icon === 'string' ? (
                    <span className="text-sm font-medium">{button.icon}</span>
                  ) : (
                    button.icon
                  )}
                </button>


              </div>
            ))}
          </React.Fragment>
        ))}
      </div>

      {/* Table dialog */}
      {showTableDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Insert Table
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Rows
                </label>
                <input
                  type="number"
                  min="2"
                  max="20"
                  value={tableRows}
                  onChange={(e) => setTableRows(parseInt(e.target.value) || 2)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Columns
                </label>
                <input
                  type="number"
                  min="2"
                  max="10"
                  value={tableCols}
                  onChange={(e) => setTableCols(parseInt(e.target.value) || 2)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowTableDialog(false)}
                className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={insertTable}
                className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Insert Table
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Click outside to close menus */}
      {showTableDialog && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => {
            setShowTableDialog(false);
          }}
        ></div>
      )}

      {/* Clear confirmation dialog */}
      <ConfirmDialog
        isOpen={showClearDialog}
        title="Clear All Content"
        message="Are you sure you want to clear all content? This action cannot be undone."
        confirmText="Clear All"
        cancelText="Cancel"
        onConfirm={() => {
          onClear();
          setShowClearDialog(false);
        }}
        onCancel={() => setShowClearDialog(false)}
        type="danger"
      />
    </div>
  );
}
