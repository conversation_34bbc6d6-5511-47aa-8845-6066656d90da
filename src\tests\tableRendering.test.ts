/**
 * Test suite for table rendering functionality
 * Ensures tables are properly rendered with correct styling and alignment
 */

import { describe, it, expect } from '@jest/test-globals';
import { processMarkdown } from '../utils/markdownProcessor';
import { processEnhancedMarkdown } from '../utils/enhancedMarkdownProcessor';

// Test markdown tables
const simpleTable = `
| Column 1 | Column 2 | Column 3 |
|----------|----------|----------|
| Cell 1   | Cell 2   | Cell 3   |
| Data A   | Data B   | Data C   |
`;

const alignedTable = `
| Left | Center | Right |
|:-----|:------:|------:|
| L1   | C1     | R1    |
| L2   | C2     | R2    |
`;

const complexTable = `
| Feature | Description | Status | Priority |
|---------|-------------|--------|----------|
| **Bold** | *Italic* text | ✅ Done | High |
| \`Code\` | [Link](url) | 🔄 In Progress | Medium |
| Long content that should wrap properly | Multiple words here | ❌ Todo | Low |
`;

describe('Table Rendering', () => {
  describe('Basic Table Processing', () => {
    it('should render simple tables with proper structure', () => {
      const html = processMarkdown(simpleTable);
      
      // Check for table wrapper
      expect(html).toContain('table-responsive-wrapper');
      expect(html).toContain('markdown-table');
      
      // Check for header structure
      expect(html).toContain('markdown-table-header');
      expect(html).toContain('markdown-table-header-cell');
      
      // Check for body structure
      expect(html).toContain('markdown-table-body');
      expect(html).toContain('markdown-table-cell');
      
      // Check content
      expect(html).toContain('Column 1');
      expect(html).toContain('Cell 1');
      expect(html).toContain('Data A');
    });

    it('should handle table alignment correctly', () => {
      const html = processMarkdown(alignedTable);
      
      // Check for alignment classes
      expect(html).toContain('text-align-center');
      expect(html).toContain('text-align-right');
      
      // Should contain the content
      expect(html).toContain('Left');
      expect(html).toContain('Center');
      expect(html).toContain('Right');
    });

    it('should handle complex table content', () => {
      const html = processMarkdown(complexTable);
      
      // Should contain formatted content
      expect(html).toContain('<strong>Bold</strong>');
      expect(html).toContain('<em>Italic</em>');
      expect(html).toContain('<code>Code</code>');
      
      // Should contain emojis
      expect(html).toContain('✅');
      expect(html).toContain('🔄');
      expect(html).toContain('❌');
      
      // Should wrap long content properly
      expect(html).toContain('Long content that should wrap properly');
    });
  });

  describe('Enhanced Table Processing', () => {
    it('should render tables with enhanced processor', () => {
      const html = processEnhancedMarkdown(simpleTable);
      
      // Check for consistent class names
      expect(html).toContain('table-responsive-wrapper');
      expect(html).toContain('markdown-table');
      expect(html).toContain('markdown-table-header-cell');
      expect(html).toContain('markdown-table-cell');
    });

    it('should maintain table structure integrity', () => {
      const html = processEnhancedMarkdown(complexTable);
      
      // Should have proper table structure
      expect(html.match(/<table/g)?.length).toBe(1);
      expect(html.match(/<\/table>/g)?.length).toBe(1);
      expect(html.match(/<thead/g)?.length).toBe(1);
      expect(html.match(/<tbody/g)?.length).toBe(1);
      
      // Should have correct number of rows
      expect(html.match(/<tr/g)?.length).toBeGreaterThanOrEqual(4); // 1 header + 3 data rows
    });
  });

  describe('Table Styling Classes', () => {
    it('should apply responsive wrapper classes', () => {
      const html = processMarkdown(simpleTable);
      
      expect(html).toMatch(/<div class="table-responsive-wrapper">/);
      expect(html).toMatch(/<table class="markdown-table">/);
    });

    it('should apply header styling classes', () => {
      const html = processMarkdown(simpleTable);
      
      expect(html).toMatch(/<thead class="markdown-table-header">/);
      expect(html).toMatch(/<th class="markdown-table-header-cell">/);
    });

    it('should apply body styling classes', () => {
      const html = processMarkdown(simpleTable);
      
      expect(html).toMatch(/<tbody class="markdown-table-body">/);
      expect(html).toMatch(/<td class="markdown-table-cell">/);
    });

    it('should handle alignment classes correctly', () => {
      const html = processMarkdown(alignedTable);
      
      // Should convert style attributes to classes
      expect(html).not.toContain('style="text-align:');
      expect(html).toContain('text-align-center');
      expect(html).toContain('text-align-right');
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty tables gracefully', () => {
      const emptyTable = `
| | |
|---|---|
| | |
      `;
      
      const html = processMarkdown(emptyTable);
      expect(html).toContain('table-responsive-wrapper');
      expect(html).toContain('markdown-table');
    });

    it('should handle tables with special characters', () => {
      const specialTable = `
| Symbol | Unicode | HTML |
|--------|---------|------|
| © | U+00A9 | &copy; |
| ™ | U+2122 | &trade; |
| € | U+20AC | &euro; |
      `;
      
      const html = processMarkdown(specialTable);
      expect(html).toContain('©');
      expect(html).toContain('™');
      expect(html).toContain('€');
    });

    it('should handle tables with code blocks', () => {
      const codeTable = `
| Language | Example |
|----------|---------|
| JavaScript | \`console.log('hello')\` |
| Python | \`print('hello')\` |
      `;
      
      const html = processMarkdown(codeTable);
      expect(html).toContain('<code>console.log');
      expect(html).toContain('<code>print');
    });

    it('should handle tables with links', () => {
      const linkTable = `
| Site | URL |
|------|-----|
| Google | [google.com](https://google.com) |
| GitHub | [github.com](https://github.com) |
      `;
      
      const html = processMarkdown(linkTable);
      expect(html).toContain('<a href="https://google.com">google.com</a>');
      expect(html).toContain('<a href="https://github.com">github.com</a>');
    });
  });

  describe('Table Accessibility', () => {
    it('should maintain proper table semantics', () => {
      const html = processMarkdown(simpleTable);
      
      // Should have proper table structure
      expect(html).toContain('<table');
      expect(html).toContain('<thead');
      expect(html).toContain('<tbody');
      expect(html).toContain('<th');
      expect(html).toContain('<td');
    });

    it('should preserve table headers for screen readers', () => {
      const html = processMarkdown(simpleTable);
      
      // Headers should be in th elements
      expect(html.match(/<th[^>]*>Column 1<\/th>/)).toBeTruthy();
      expect(html.match(/<th[^>]*>Column 2<\/th>/)).toBeTruthy();
      expect(html.match(/<th[^>]*>Column 3<\/th>/)).toBeTruthy();
    });
  });
});

// Export test utilities for manual testing
export const tableTestUtils = {
  simpleTable,
  alignedTable,
  complexTable,
  
  testTableRendering() {
    console.log('Testing table rendering...');
    
    const tests = [
      { name: 'Simple Table', markdown: simpleTable },
      { name: 'Aligned Table', markdown: alignedTable },
      { name: 'Complex Table', markdown: complexTable },
    ];
    
    tests.forEach(test => {
      console.log(`\n--- ${test.name} ---`);
      const html = processMarkdown(test.markdown);
      console.log('HTML Output:', html);
      
      // Check for required classes
      const hasWrapper = html.includes('table-responsive-wrapper');
      const hasTable = html.includes('markdown-table');
      const hasHeader = html.includes('markdown-table-header');
      const hasBody = html.includes('markdown-table-body');
      
      console.log('Validation:', {
        hasWrapper,
        hasTable,
        hasHeader,
        hasBody,
        isValid: hasWrapper && hasTable && hasHeader && hasBody
      });
    });
  }
};
