/**
 * Performance Monitoring Hook
 * Tracks app performance and user experience metrics
 */

'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { PerformanceMonitor, MemoryManager, MarkdownCache } from '@/utils/performanceOptimizations';

interface PerformanceMetrics {
  renderTime: number;
  memoryUsage: { total: number; components: number };
  cacheStats: { size: number; maxSize: number; hitRate: number };
  networkLatency: number;
  errorCount: number;
  userInteractions: number;
}

interface PerformanceConfig {
  enableMemoryTracking?: boolean;
  enableNetworkTracking?: boolean;
  enableUserTracking?: boolean;
  reportingInterval?: number;
  maxMetricHistory?: number;
}

export function usePerformanceMonitoring(config: PerformanceConfig = {}) {
  const {
    enableMemoryTracking = true,
    enableNetworkTracking = true,
    enableUserTracking = true,
    reportingInterval = 30000, // 30 seconds
    maxMetricHistory = 100
  } = config;

  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    memoryUsage: { total: 0, components: 0 },
    cacheStats: { size: 0, maxSize: 0, hitRate: 0 },
    networkLatency: 0,
    errorCount: 0,
    userInteractions: 0
  });

  const [isMonitoring, setIsMonitoring] = useState(false);
  const performanceMonitor = useRef(PerformanceMonitor.getInstance());
  const memoryManager = useRef(MemoryManager.getInstance());
  const markdownCache = useRef(MarkdownCache.getInstance());
  const reportingTimer = useRef<NodeJS.Timeout>();
  const userInteractionCount = useRef(0);
  const componentId = useRef(`perf-monitor-${Date.now()}`);

  // Start performance monitoring
  const startMonitoring = useCallback(() => {
    if (isMonitoring) return;

    setIsMonitoring(true);
    console.log('Performance monitoring started');

    // Set up periodic reporting
    reportingTimer.current = setInterval(() => {
      updateMetrics();
    }, reportingInterval);

    // Track component memory usage
    if (enableMemoryTracking) {
      memoryManager.current.trackMemoryUsage(componentId.current, 1024); // 1KB base
    }

    // Set up user interaction tracking
    if (enableUserTracking) {
      setupUserInteractionTracking();
    }

    // Set up network tracking
    if (enableNetworkTracking) {
      setupNetworkTracking();
    }

  }, [isMonitoring, reportingInterval, enableMemoryTracking, enableUserTracking, enableNetworkTracking]);

  // Stop performance monitoring
  const stopMonitoring = useCallback(() => {
    if (!isMonitoring) return;

    setIsMonitoring(false);
    console.log('Performance monitoring stopped');

    // Clear reporting timer
    if (reportingTimer.current) {
      clearInterval(reportingTimer.current);
    }

    // Clean up memory tracking
    memoryManager.current.cleanup(componentId.current);

    // Remove event listeners
    removeEventListeners();

  }, [isMonitoring]);

  // Update metrics
  const updateMetrics = useCallback(() => {
    const newMetrics: PerformanceMetrics = {
      renderTime: performanceMonitor.current.getMetricStats('render_time')?.avg || 0,
      memoryUsage: memoryManager.current.getMemoryUsage(),
      cacheStats: markdownCache.current.getStats(),
      networkLatency: performanceMonitor.current.getMetricStats('network_latency')?.avg || 0,
      errorCount: performanceMonitor.current.getMetricStats('errors')?.count || 0,
      userInteractions: userInteractionCount.current
    };

    setMetrics(newMetrics);

    // Log performance warnings
    checkPerformanceThresholds(newMetrics);

  }, []);

  // Check performance thresholds and log warnings
  const checkPerformanceThresholds = useCallback((metrics: PerformanceMetrics) => {
    // Render time threshold (100ms)
    if (metrics.renderTime > 100) {
      console.warn(`Performance Warning: Slow render time ${metrics.renderTime.toFixed(2)}ms`);
    }

    // Memory usage threshold (50MB)
    if (metrics.memoryUsage.total > 50 * 1024 * 1024) {
      console.warn(`Performance Warning: High memory usage ${(metrics.memoryUsage.total / 1024 / 1024).toFixed(2)}MB`);
    }

    // Network latency threshold (1000ms)
    if (metrics.networkLatency > 1000) {
      console.warn(`Performance Warning: High network latency ${metrics.networkLatency.toFixed(2)}ms`);
    }

    // Error count threshold
    if (metrics.errorCount > 10) {
      console.warn(`Performance Warning: High error count ${metrics.errorCount}`);
    }

  }, []);

  // Set up user interaction tracking
  const setupUserInteractionTracking = useCallback(() => {
    const trackInteraction = () => {
      userInteractionCount.current++;
      performanceMonitor.current.recordMetric('user_interactions', 1);
    };

    // Track various user interactions
    document.addEventListener('click', trackInteraction);
    document.addEventListener('keydown', trackInteraction);
    document.addEventListener('scroll', trackInteraction);
    document.addEventListener('touchstart', trackInteraction);

    // Store references for cleanup
    (window as any).__perfMonitorListeners = {
      click: trackInteraction,
      keydown: trackInteraction,
      scroll: trackInteraction,
      touchstart: trackInteraction
    };

  }, []);

  // Set up network tracking
  const setupNetworkTracking = useCallback(() => {
    // Override fetch to track network requests
    const originalFetch = window.fetch;
    
    window.fetch = async (...args) => {
      const start = performance.now();
      
      try {
        const response = await originalFetch(...args);
        const end = performance.now();
        
        performanceMonitor.current.recordMetric('network_latency', end - start);
        performanceMonitor.current.recordMetric('network_requests', 1);
        
        if (!response.ok) {
          performanceMonitor.current.recordMetric('network_errors', 1);
        }
        
        return response;
      } catch (error) {
        const end = performance.now();
        performanceMonitor.current.recordMetric('network_latency', end - start);
        performanceMonitor.current.recordMetric('network_errors', 1);
        throw error;
      }
    };

    // Store original fetch for cleanup
    (window as any).__originalFetch = originalFetch;

  }, []);

  // Remove event listeners
  const removeEventListeners = useCallback(() => {
    const listeners = (window as any).__perfMonitorListeners;
    if (listeners) {
      document.removeEventListener('click', listeners.click);
      document.removeEventListener('keydown', listeners.keydown);
      document.removeEventListener('scroll', listeners.scroll);
      document.removeEventListener('touchstart', listeners.touchstart);
      delete (window as any).__perfMonitorListeners;
    }

    // Restore original fetch
    const originalFetch = (window as any).__originalFetch;
    if (originalFetch) {
      window.fetch = originalFetch;
      delete (window as any).__originalFetch;
    }

  }, []);

  // Measure component render time
  const measureRenderTime = useCallback((componentName: string, renderFn: () => void) => {
    return performanceMonitor.current.measureExecution(`render_time_${componentName}`, renderFn);
  }, []);

  // Record custom metric
  const recordMetric = useCallback((name: string, value: number) => {
    performanceMonitor.current.recordMetric(name, value);
  }, []);

  // Get all performance metrics
  const getAllMetrics = useCallback(() => {
    return performanceMonitor.current.getAllMetrics();
  }, []);

  // Clear cache
  const clearCache = useCallback(() => {
    markdownCache.current.clear();
    console.log('Performance cache cleared');
  }, []);

  // Force garbage collection
  const forceGarbageCollection = useCallback(() => {
    memoryManager.current.cleanup(componentId.current);
    
    // Force browser GC if available
    if ('gc' in window && typeof (window as any).gc === 'function') {
      (window as any).gc();
    }
    
    console.log('Garbage collection forced');
  }, []);

  // Export performance report
  const exportReport = useCallback(() => {
    const report = {
      timestamp: new Date().toISOString(),
      metrics: metrics,
      allMetrics: getAllMetrics(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      connection: (navigator as any).connection ? {
        effectiveType: (navigator as any).connection.effectiveType,
        downlink: (navigator as any).connection.downlink,
        rtt: (navigator as any).connection.rtt
      } : null
    };

    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `performance-report-${Date.now()}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
    console.log('Performance report exported');

  }, [metrics, getAllMetrics]);

  // Initialize monitoring on mount
  useEffect(() => {
    startMonitoring();
    
    return () => {
      stopMonitoring();
    };
  }, [startMonitoring, stopMonitoring]);

  // Update metrics on mount and periodically
  useEffect(() => {
    updateMetrics();
  }, [updateMetrics]);

  return {
    metrics,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    measureRenderTime,
    recordMetric,
    getAllMetrics,
    clearCache,
    forceGarbageCollection,
    exportReport,
    updateMetrics
  };
}
