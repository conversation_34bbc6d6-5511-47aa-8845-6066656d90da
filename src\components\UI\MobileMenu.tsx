/**
 * Mobile menu component for organizing complex features
 */

'use client';

import React, { useState } from 'react';
import { useApp } from '@/contexts/AppContext';
import { useResponsive } from '@/hooks/useResponsive';

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
}

export function MobileMenu({ isOpen, onClose }: MobileMenuProps) {
  const { state, updateSettings } = useApp();
  const responsive = useResponsive();
  const [activeSection, setActiveSection] = useState<'main' | 'settings' | 'export' | 'help'>('main');

  if (!responsive.isMobile || !isOpen) return null;

  const renderMainMenu = () => (
    <div className="space-y-2">
      <button
        onClick={() => setActiveSection('settings')}
        className="w-full flex items-center gap-3 p-4 text-left hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
      >
        <svg className="w-6 h-6 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
        <div>
          <div className="font-medium text-gray-900 dark:text-white">Settings</div>
          <div className="text-sm text-gray-500 dark:text-gray-400">Theme, font size, preferences</div>
        </div>
      </button>

      <button
        onClick={() => setActiveSection('export')}
        className="w-full flex items-center gap-3 p-4 text-left hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
      >
        <svg className="w-6 h-6 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <div>
          <div className="font-medium text-gray-900 dark:text-white">Export</div>
          <div className="text-sm text-gray-500 dark:text-gray-400">PDF, DOCX, TXT formats</div>
        </div>
      </button>

      <button
        onClick={() => setActiveSection('help')}
        className="w-full flex items-center gap-3 p-4 text-left hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
      >
        <svg className="w-6 h-6 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <div>
          <div className="font-medium text-gray-900 dark:text-white">Help & Tips</div>
          <div className="text-sm text-gray-500 dark:text-gray-400">Markdown guide, shortcuts</div>
        </div>
      </button>
    </div>
  );

  const renderSettingsMenu = () => (
    <div className="space-y-4">
      <button
        onClick={() => setActiveSection('main')}
        className="flex items-center gap-2 text-blue-600 dark:text-blue-400 mb-4"
      >
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
        </svg>
        Back to Menu
      </button>

      {/* Theme Toggle */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Theme</label>
        <button
          onClick={() => updateSettings({ theme: state.settings.theme === 'dark' ? 'light' : 'dark' })}
          className="w-full flex items-center justify-between p-3 bg-gray-100 dark:bg-gray-700 rounded-lg"
        >
          <span className="text-gray-900 dark:text-white">
            {state.settings.theme === 'dark' ? 'Dark Mode' : 'Light Mode'}
          </span>
          <div className={`w-12 h-6 rounded-full transition-colors ${
            state.settings.theme === 'dark' ? 'bg-blue-600' : 'bg-gray-300'
          }`}>
            <div className={`w-5 h-5 bg-white rounded-full shadow-md transform transition-transform mt-0.5 ${
              state.settings.theme === 'dark' ? 'translate-x-6' : 'translate-x-0.5'
            }`} />
          </div>
        </button>
      </div>

      {/* Font Size */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Font Size</label>
        <div className="flex items-center gap-2">
          <button
            onClick={() => updateSettings({ fontSize: Math.max(12, state.settings.fontSize - 1) })}
            className="p-2 bg-gray-100 dark:bg-gray-700 rounded-lg"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
            </svg>
          </button>
          <span className="flex-1 text-center text-gray-900 dark:text-white">
            {state.settings.fontSize}px
          </span>
          <button
            onClick={() => updateSettings({ fontSize: Math.min(24, state.settings.fontSize + 1) })}
            className="p-2 bg-gray-100 dark:bg-gray-700 rounded-lg"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
          </button>
        </div>
      </div>

      {/* Auto Save */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Auto Save</label>
        <button
          onClick={() => updateSettings({ autoSave: !state.settings.autoSave })}
          className="w-full flex items-center justify-between p-3 bg-gray-100 dark:bg-gray-700 rounded-lg"
        >
          <span className="text-gray-900 dark:text-white">
            {state.settings.autoSave ? 'Enabled' : 'Disabled'}
          </span>
          <div className={`w-12 h-6 rounded-full transition-colors ${
            state.settings.autoSave ? 'bg-blue-600' : 'bg-gray-300'
          }`}>
            <div className={`w-5 h-5 bg-white rounded-full shadow-md transform transition-transform mt-0.5 ${
              state.settings.autoSave ? 'translate-x-6' : 'translate-x-0.5'
            }`} />
          </div>
        </button>
      </div>
    </div>
  );

  const renderExportMenu = () => (
    <div className="space-y-4">
      <button
        onClick={() => setActiveSection('main')}
        className="flex items-center gap-2 text-blue-600 dark:text-blue-400 mb-4"
      >
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
        </svg>
        Back to Menu
      </button>

      <div className="space-y-2">
        {[
          { format: 'PDF', icon: '📄', description: 'Portable Document Format' },
          { format: 'DOCX', icon: '📝', description: 'Microsoft Word Document' },
          { format: 'TXT', icon: '📋', description: 'Plain Text File' },
          { format: 'MD', icon: '📖', description: 'Markdown File' }
        ].map(({ format, icon, description }) => (
          <button
            key={format}
            onClick={() => {
              // Export functionality would be implemented here
              alert(`Export as ${format} - Feature coming soon!`);
            }}
            className="w-full flex items-center gap-3 p-4 text-left hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <span className="text-2xl">{icon}</span>
            <div>
              <div className="font-medium text-gray-900 dark:text-white">{format}</div>
              <div className="text-sm text-gray-500 dark:text-gray-400">{description}</div>
            </div>
          </button>
        ))}
      </div>
    </div>
  );

  const renderHelpMenu = () => (
    <div className="space-y-4">
      <button
        onClick={() => setActiveSection('main')}
        className="flex items-center gap-2 text-blue-600 dark:text-blue-400 mb-4"
      >
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
        </svg>
        Back to Menu
      </button>

      <div className="space-y-4">
        <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-2">Quick Tips</h3>
          <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
            <li>• Use **text** for bold</li>
            <li>• Use *text* for italic</li>
            <li>• Use # for headings</li>
            <li>• Use - for bullet lists</li>
          </ul>
        </div>

        <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <h3 className="font-medium text-green-900 dark:text-green-100 mb-2">Keyboard Shortcuts</h3>
          <ul className="text-sm text-green-800 dark:text-green-200 space-y-1">
            <li>• Ctrl+B: Bold text</li>
            <li>• Ctrl+I: Italic text</li>
            <li>• Ctrl+K: Insert link</li>
            <li>• Ctrl+Z: Undo</li>
          </ul>
        </div>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeSection) {
      case 'settings':
        return renderSettingsMenu();
      case 'export':
        return renderExportMenu();
      case 'help':
        return renderHelpMenu();
      default:
        return renderMainMenu();
    }
  };

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-40"
        onClick={onClose}
      />
      
      {/* Menu Panel */}
      <div className="fixed right-0 top-0 bottom-0 w-80 bg-white dark:bg-gray-800 z-50 transform transition-transform duration-300 overflow-y-auto">
        {/* Header */}
        <div className="h-14 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600 flex items-center px-4">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            {activeSection === 'main' ? 'Menu' : 
             activeSection === 'settings' ? 'Settings' :
             activeSection === 'export' ? 'Export' : 'Help'}
          </h2>
          <button
            onClick={onClose}
            className="ml-auto p-2 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-4">
          {renderContent()}
        </div>
      </div>
    </>
  );
}
