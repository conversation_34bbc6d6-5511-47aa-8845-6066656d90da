# Test Markdown Document

This is a test document to verify that all markdown elements are rendering properly.

## Headers

### Level 3 Header
#### Level 4 Header
##### Level 5 Header
###### Level 6 Header

## Text Formatting

**Bold text** and *italic text* and ***bold italic text***.

`Inline code` and regular text.

## Links

[External link](https://example.com)
[Internal link](#headers)

## Code Blocks

```javascript
function hello() {
  console.log("Hello, World!");
  return "success";
}

const data = {
  name: "test",
  value: 42
};
```

```python
def greet(name):
    print(f"Hello, {name}!")
    return True

# This is a comment
numbers = [1, 2, 3, 4, 5]
```

```
Plain text code block
No syntax highlighting
Multiple lines
```

## Lists

### Unordered List
- Item 1
- Item 2
  - Nested item 1
  - Nested item 2
- Item 3

### Ordered List
1. First item
2. Second item
   1. Nested item
   2. Another nested item
3. Third item

## Blockquotes

> This is a blockquote.
> It can span multiple lines.
> 
> And have multiple paragraphs.

## Tables

| Name | Age | City |
|------|-----|------|
| John | 25 | New York |
| Jane | 30 | London |
| Bob | 35 | Paris |

## Images

![Alt text](https://via.placeholder.com/300x200)

## Horizontal Rule

---

## Mixed Content

Here's a paragraph with **bold**, *italic*, and `inline code`. You can also have [links](https://example.com) in the same paragraph.

```bash
# Command line example
npm install
npm run dev
```

That's the end of our test document!
