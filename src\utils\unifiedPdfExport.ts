/**
 * Unified PDF Export Service
 * Provides multiple PDF generation methods with fallbacks and quality options
 */

import { PuppeteerPdfExporter, PuppeteerPdfOptions } from './puppeteerPdfExport';
import { PlaywrightPdfExporter, PlaywrightPdfOptions } from './playwrightPdfExport';
import { MarkdownPdfExporter, MarkdownPdfOptions } from './markdownPdfExport';

export type PdfExportMethod = 'puppeteer' | 'playwright' | 'markdown-pdf' | 'auto';
export type PdfQuality = 'high' | 'medium' | 'low';
export type PdfFormat = 'A4' | 'A3' | 'A5' | 'Letter' | 'Legal' | 'Tabloid';
export type PdfOrientation = 'portrait' | 'landscape';

export interface UnifiedPdfOptions {
  method?: PdfExportMethod;
  title?: string;
  author?: string;
  subject?: string;
  keywords?: string;
  format?: PdfFormat;
  orientation?: PdfOrientation;
  margin?: {
    top?: string;
    right?: string;
    bottom?: string;
    left?: string;
  };
  quality?: PdfQuality;
  timeout?: number;
  enableFallback?: boolean;
  displayHeaderFooter?: boolean;
  headerTemplate?: string;
  footerTemplate?: string;
  printBackground?: boolean;
  scale?: number;
  preferCSSPageSize?: boolean;
  tagged?: boolean; // For accessibility (Playwright)
  outline?: boolean; // For document outline (Playwright)
}

export interface PdfExportResult {
  success: boolean;
  data?: Uint8Array;
  method?: PdfExportMethod;
  error?: string;
  fileSize?: number;
  generationTime?: number;
}

export class UnifiedPdfExportService {
  private static instance: UnifiedPdfExportService;
  private activeExporters: Map<PdfExportMethod, any> = new Map();

  private constructor() {}

  public static getInstance(): UnifiedPdfExportService {
    if (!UnifiedPdfExportService.instance) {
      UnifiedPdfExportService.instance = new UnifiedPdfExportService();
    }
    return UnifiedPdfExportService.instance;
  }

  private async getExporter(method: PdfExportMethod, options: UnifiedPdfOptions) {
    const key = method;
    
    if (this.activeExporters.has(key)) {
      return this.activeExporters.get(key);
    }

    let exporter;
    
    switch (method) {
      case 'puppeteer':
        exporter = new PuppeteerPdfExporter(this.mapToPuppeteerOptions(options));
        break;
      case 'playwright':
        exporter = new PlaywrightPdfExporter(this.mapToPlaywrightOptions(options));
        break;
      case 'markdown-pdf':
        exporter = new MarkdownPdfExporter(this.mapToMarkdownPdfOptions(options));
        break;
      default:
        throw new Error(`Unsupported export method: ${method}`);
    }

    this.activeExporters.set(key, exporter);
    return exporter;
  }

  private mapToPuppeteerOptions(options: UnifiedPdfOptions): PuppeteerPdfOptions {
    return {
      title: options.title,
      author: options.author,
      subject: options.subject,
      keywords: options.keywords,
      format: options.format,
      orientation: options.orientation,
      margin: options.margin,
      quality: options.quality,
      timeout: options.timeout,
      displayHeaderFooter: options.displayHeaderFooter,
      headerTemplate: options.headerTemplate,
      footerTemplate: options.footerTemplate,
      printBackground: options.printBackground,
      scale: options.scale,
      preferCSSPageSize: options.preferCSSPageSize,
    };
  }

  private mapToPlaywrightOptions(options: UnifiedPdfOptions): PlaywrightPdfOptions {
    return {
      title: options.title,
      author: options.author,
      subject: options.subject,
      keywords: options.keywords,
      format: options.format,
      orientation: options.orientation,
      margin: options.margin,
      quality: options.quality,
      timeout: options.timeout,
      displayHeaderFooter: options.displayHeaderFooter,
      headerTemplate: options.headerTemplate,
      footerTemplate: options.footerTemplate,
      printBackground: options.printBackground,
      scale: options.scale,
      preferCSSPageSize: options.preferCSSPageSize,
      tagged: options.tagged,
      outline: options.outline,
    };
  }

  private mapToMarkdownPdfOptions(options: UnifiedPdfOptions): MarkdownPdfOptions {
    return {
      title: options.title,
      author: options.author,
      subject: options.subject,
      keywords: options.keywords,
      format: options.format as any,
      orientation: options.orientation,
      border: options.margin,
      quality: options.quality,
      timeout: options.timeout,
    };
  }

  private determineBestMethod(options: UnifiedPdfOptions): PdfExportMethod {
    // Auto-select the best method based on requirements and environment
    if (options.method && options.method !== 'auto') {
      return options.method;
    }

    // Prefer Playwright for high quality and accessibility features
    if (options.quality === 'high' || options.tagged || options.outline) {
      return 'playwright';
    }

    // Use Puppeteer for medium quality and good performance
    if (options.quality === 'medium') {
      return 'puppeteer';
    }

    // Use markdown-pdf for simple documents and low resource usage
    return 'markdown-pdf';
  }

  private async tryExportWithMethod(
    markdown: string,
    method: PdfExportMethod,
    options: UnifiedPdfOptions
  ): Promise<PdfExportResult> {
    const startTime = Date.now();
    
    try {
      const exporter = await this.getExporter(method, options);
      const data = await exporter.exportMarkdownToPdf(markdown);
      const generationTime = Date.now() - startTime;
      
      return {
        success: true,
        data,
        method,
        fileSize: data.length,
        generationTime,
      };
    } catch (error) {
      const generationTime = Date.now() - startTime;
      console.error(`PDF export failed with ${method}:`, error);
      
      return {
        success: false,
        method,
        error: error instanceof Error ? error.message : 'Unknown error',
        generationTime,
      };
    }
  }

  public async exportToPdf(
    markdown: string,
    options: UnifiedPdfOptions = {}
  ): Promise<PdfExportResult> {
    const enableFallback = options.enableFallback !== false;
    const primaryMethod = this.determineBestMethod(options);
    
    // Try primary method
    let result = await this.tryExportWithMethod(markdown, primaryMethod, options);
    
    if (result.success || !enableFallback) {
      return result;
    }

    // Try fallback methods if primary failed
    const fallbackMethods: PdfExportMethod[] = ['playwright', 'puppeteer', 'markdown-pdf']
      .filter(method => method !== primaryMethod);

    for (const method of fallbackMethods) {
      console.log(`Trying fallback method: ${method}`);
      result = await this.tryExportWithMethod(markdown, method, options);
      
      if (result.success) {
        console.log(`Successfully exported with fallback method: ${method}`);
        return result;
      }
    }

    // All methods failed
    return {
      success: false,
      error: 'All PDF export methods failed',
    };
  }

  public async exportAndDownload(
    markdown: string,
    filename: string,
    options: UnifiedPdfOptions = {}
  ): Promise<PdfExportResult> {
    const result = await this.exportToPdf(markdown, options);
    
    if (result.success && result.data) {
      this.downloadPdf(result.data, filename);
    }
    
    return result;
  }

  public downloadPdf(pdfData: Uint8Array, filename: string): void {
    const blob = new Blob([pdfData], { type: 'application/pdf' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = filename.replace(/\.[^/.]+$/, '.pdf');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Clean up
    setTimeout(() => URL.revokeObjectURL(url), 1000);
  }

  public async cleanup(): Promise<void> {
    const cleanupPromises: Promise<void>[] = [];
    
    for (const [method, exporter] of this.activeExporters) {
      if (exporter && typeof exporter.close === 'function') {
        cleanupPromises.push(exporter.close());
      }
    }
    
    await Promise.all(cleanupPromises);
    this.activeExporters.clear();
  }

  public getAvailableMethods(): PdfExportMethod[] {
    return ['puppeteer', 'playwright', 'markdown-pdf'];
  }

  public getMethodInfo(method: PdfExportMethod): { name: string; description: string; pros: string[]; cons: string[] } {
    const methodInfo = {
      puppeteer: {
        name: 'Puppeteer',
        description: 'High-quality PDF generation using Chrome/Chromium',
        pros: [
          'Excellent rendering quality',
          'Full CSS support',
          'Text selectable',
          'Good performance',
          'Reliable'
        ],
        cons: [
          'Larger bundle size',
          'Requires Chrome/Chromium',
          'Higher memory usage'
        ]
      },
      playwright: {
        name: 'Playwright',
        description: 'Premium PDF generation with accessibility features',
        pros: [
          'Best rendering quality',
          'Accessibility support (tagged PDFs)',
          'Document outline support',
          'Text selectable',
          'Cross-browser support'
        ],
        cons: [
          'Largest bundle size',
          'Highest memory usage',
          'Slower startup'
        ]
      },
      'markdown-pdf': {
        name: 'Markdown-PDF',
        description: 'Direct markdown to PDF conversion',
        pros: [
          'Lightweight',
          'Fast processing',
          'Good for simple documents',
          'Text selectable'
        ],
        cons: [
          'Limited CSS support',
          'Basic styling options',
          'May have rendering issues with complex content'
        ]
      }
    };

    return methodInfo[method] || {
      name: 'Unknown',
      description: 'Unknown method',
      pros: [],
      cons: []
    };
  }
}

// Export singleton instance
export const pdfExportService = UnifiedPdfExportService.getInstance();

// Convenience functions
export async function exportMarkdownToPdf(
  markdown: string,
  filename: string,
  options: UnifiedPdfOptions = {}
): Promise<PdfExportResult> {
  return pdfExportService.exportAndDownload(markdown, filename, options);
}

export async function generatePdfData(
  markdown: string,
  options: UnifiedPdfOptions = {}
): Promise<PdfExportResult> {
  return pdfExportService.exportToPdf(markdown, options);
}
