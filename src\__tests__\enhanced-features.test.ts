/**
 * Comprehensive Tests for Enhanced Markdown Editor Features
 * Tests PDF export, enhanced markdown, mobile UI, and performance optimizations
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';

// Mock modules
jest.mock('katex', () => ({
  renderToString: jest.fn((math, options) => `<span class="katex">${math}</span>`)
}));

jest.mock('mermaid', () => ({
  initialize: jest.fn(),
  render: jest.fn().mockResolvedValue({ svg: '<svg>test diagram</svg>' })
}));

jest.mock('highlight.js', () => ({
  highlight: jest.fn((code, options) => ({ value: `<span class="hljs">${code}</span>` })),
  highlightAuto: jest.fn((code) => ({ value: `<span class="hljs">${code}</span>` })),
  getLanguage: jest.fn(() => true)
}));

// Import components and utilities to test
import { enhancedMarkdownToHtml, processEnhancedMarkdown } from '../utils/enhancedMarkdownProcessor';
import { EnhancedPdfExporter, exportAsEnhancedPdf } from '../utils/enhancedPdfExport';
import { MemoryManager, MarkdownCache, PerformanceMonitor } from '../utils/performanceOptimizations';

describe('Enhanced Markdown Processing', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
  });

  it('should process math expressions correctly', () => {
    const markdown = 'Inline math: $E = mc^2$ and display math: $$\\int_0^1 x dx$$';
    const html = enhancedMarkdownToHtml(markdown);
    
    expect(html).toContain('class="math-inline"');
    expect(html).toContain('class="math-display"');
    expect(html).toContain('E = mc^2');
  });

  it('should process mermaid diagrams', () => {
    const markdown = '```mermaid\ngraph TD\nA --> B\n```';
    const html = processEnhancedMarkdown(markdown);
    
    expect(html).toContain('class="mermaid-diagram"');
    expect(html).toContain('data-diagram=');
  });

  it('should handle custom containers', () => {
    const markdown = '::: warning Important Note\nThis is a warning message\n:::';
    const html = enhancedMarkdownToHtml(markdown);
    
    expect(html).toContain('class="custom-container warning"');
    expect(html).toContain('container-icon');
    expect(html).toContain('Important Note');
  });

  it('should process enhanced text formatting', () => {
    const markdown = '==highlighted== text and ++inserted++ text and ~subscript~ and ^superscript^';
    const html = enhancedMarkdownToHtml(markdown);
    
    expect(html).toContain('class="md-highlight"');
    expect(html).toContain('class="md-inserted"');
    expect(html).toContain('class="md-subscript"');
    expect(html).toContain('class="md-superscript"');
  });

  it('should handle enhanced task lists', () => {
    const markdown = '- [x] Completed task\n- [ ] Pending task\n- [/] In progress\n- [-] Cancelled';
    const html = enhancedMarkdownToHtml(markdown);
    
    expect(html).toContain('task-completed');
    expect(html).toContain('task-pending');
    expect(html).toContain('task-in-progress');
    expect(html).toContain('task-cancelled');
  });

  it('should add copy buttons to code blocks', () => {
    const markdown = '```javascript\nconsole.log("Hello World");\n```';
    const html = processEnhancedMarkdown(markdown);
    
    expect(html).toContain('enhanced-code-block');
    expect(html).toContain('copy-code-btn');
    expect(html).toContain('javascript');
  });

  it('should handle syntax highlighting', () => {
    const markdown = '```python\nprint("Hello World")\n```';
    const html = processEnhancedMarkdown(markdown);
    
    expect(html).toContain('hljs');
    expect(html).toContain('python');
  });
});

describe('Enhanced PDF Export', () => {
  let mockCanvas: any;
  let mockContext: any;

  beforeEach(() => {
    // Mock canvas and context
    mockContext = {
      drawImage: jest.fn(),
      getImageData: jest.fn(),
      putImageData: jest.fn()
    };

    mockCanvas = {
      getContext: jest.fn(() => mockContext),
      toDataURL: jest.fn(() => 'data:image/jpeg;base64,test'),
      width: 800,
      height: 600
    };

    // Mock document.createElement for canvas
    const originalCreateElement = document.createElement;
    document.createElement = jest.fn((tagName) => {
      if (tagName === 'canvas') {
        return mockCanvas;
      }
      return originalCreateElement.call(document, tagName);
    });

    // Mock html2canvas
    (global as any).html2canvas = jest.fn().mockResolvedValue(mockCanvas);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('should create PDF exporter with default options', () => {
    const exporter = new EnhancedPdfExporter();
    expect(exporter).toBeDefined();
  });

  it('should create PDF exporter with custom options', () => {
    const options = {
      title: 'Test Document',
      author: 'Test Author',
      fontSize: 14,
      quality: 0.9,
      compression: true
    };
    
    const exporter = new EnhancedPdfExporter(options);
    expect(exporter).toBeDefined();
  });

  it('should export markdown to PDF', async () => {
    const markdown = '# Test Document\n\nThis is a test.';
    const filename = 'test.md';
    
    // Mock jsPDF
    const mockSave = jest.fn();
    const mockAddImage = jest.fn();
    const mockAddPage = jest.fn();
    
    (global as any).jsPDF = jest.fn(() => ({
      save: mockSave,
      addImage: mockAddImage,
      addPage: mockAddPage,
      output: jest.fn(() => new Uint8Array()),
      setProperties: jest.fn(),
      internal: {
        pageSize: {
          getWidth: () => 210,
          getHeight: () => 297
        }
      }
    }));

    await exportAsEnhancedPdf(markdown, filename);
    
    // Verify PDF creation was attempted
    expect((global as any).jsPDF).toHaveBeenCalled();
  });

  it('should handle PDF export errors gracefully', async () => {
    const markdown = '# Test Document';
    const filename = 'test.md';
    
    // Mock error in PDF creation
    (global as any).jsPDF = jest.fn(() => {
      throw new Error('PDF creation failed');
    });

    await expect(exportAsEnhancedPdf(markdown, filename)).rejects.toThrow('Failed to export enhanced PDF');
  });
});

describe('Performance Optimizations', () => {
  let memoryManager: MemoryManager;
  let markdownCache: MarkdownCache;
  let performanceMonitor: PerformanceMonitor;

  beforeEach(() => {
    memoryManager = MemoryManager.getInstance();
    markdownCache = MarkdownCache.getInstance();
    performanceMonitor = PerformanceMonitor.getInstance();
  });

  describe('Memory Manager', () => {
    it('should track memory usage', () => {
      memoryManager.trackMemoryUsage('test-component', 1024);
      const usage = memoryManager.getMemoryUsage();
      
      expect(usage.total).toBe(1024);
      expect(usage.components).toBe(1);
    });

    it('should register and execute cleanup tasks', () => {
      const cleanup = jest.fn();
      memoryManager.registerCleanup('test-component', cleanup);
      memoryManager.cleanup('test-component');
      
      expect(cleanup).toHaveBeenCalled();
    });

    it('should perform garbage collection when threshold exceeded', () => {
      // Add multiple components to exceed threshold
      for (let i = 0; i < 100; i++) {
        memoryManager.trackMemoryUsage(`component-${i}`, 1024 * 1024); // 1MB each
      }
      
      const usage = memoryManager.getMemoryUsage();
      expect(usage.components).toBeLessThan(100); // Some should be cleaned up
    });
  });

  describe('Markdown Cache', () => {
    it('should cache and retrieve markdown', () => {
      const markdown = '# Test';
      const html = '<h1>Test</h1>';
      
      markdownCache.set(markdown, html);
      const cached = markdownCache.get(markdown);
      
      expect(cached).toBe(html);
    });

    it('should return null for non-existent cache', () => {
      const cached = markdownCache.get('non-existent');
      expect(cached).toBeNull();
    });

    it('should clear cache', () => {
      markdownCache.set('test', '<p>test</p>');
      markdownCache.clear();
      
      const cached = markdownCache.get('test');
      expect(cached).toBeNull();
    });

    it('should provide cache statistics', () => {
      markdownCache.clear();
      markdownCache.set('test1', '<p>test1</p>');
      markdownCache.set('test2', '<p>test2</p>');
      
      const stats = markdownCache.getStats();
      expect(stats.size).toBe(2);
      expect(stats.maxSize).toBeGreaterThan(0);
    });
  });

  describe('Performance Monitor', () => {
    it('should record metrics', () => {
      performanceMonitor.recordMetric('test-metric', 100);
      const stats = performanceMonitor.getMetricStats('test-metric');
      
      expect(stats).toBeDefined();
      expect(stats!.avg).toBe(100);
      expect(stats!.count).toBe(1);
    });

    it('should measure execution time', () => {
      const result = performanceMonitor.measureExecution('test-function', () => {
        return 'test-result';
      });
      
      expect(result).toBe('test-result');
      
      const stats = performanceMonitor.getMetricStats('test-function');
      expect(stats).toBeDefined();
      expect(stats!.count).toBe(1);
    });

    it('should calculate metric statistics', () => {
      performanceMonitor.recordMetric('test-stats', 10);
      performanceMonitor.recordMetric('test-stats', 20);
      performanceMonitor.recordMetric('test-stats', 30);
      
      const stats = performanceMonitor.getMetricStats('test-stats');
      expect(stats!.avg).toBe(20);
      expect(stats!.min).toBe(10);
      expect(stats!.max).toBe(30);
      expect(stats!.count).toBe(3);
    });

    it('should get all metrics', () => {
      performanceMonitor.recordMetric('metric1', 100);
      performanceMonitor.recordMetric('metric2', 200);
      
      const allMetrics = performanceMonitor.getAllMetrics();
      expect(allMetrics).toHaveProperty('metric1');
      expect(allMetrics).toHaveProperty('metric2');
    });
  });
});

describe('Utility Functions', () => {
  it('should debounce function calls', (done) => {
    const { debounce } = require('../utils/performanceOptimizations');
    const mockFn = jest.fn();
    const debouncedFn = debounce(mockFn, 100);
    
    debouncedFn();
    debouncedFn();
    debouncedFn();
    
    expect(mockFn).not.toHaveBeenCalled();
    
    setTimeout(() => {
      expect(mockFn).toHaveBeenCalledTimes(1);
      done();
    }, 150);
  });

  it('should throttle function calls', (done) => {
    const { throttle } = require('../utils/performanceOptimizations');
    const mockFn = jest.fn();
    const throttledFn = throttle(mockFn, 100);
    
    throttledFn();
    throttledFn();
    throttledFn();
    
    expect(mockFn).toHaveBeenCalledTimes(1);
    
    setTimeout(() => {
      throttledFn();
      expect(mockFn).toHaveBeenCalledTimes(2);
      done();
    }, 150);
  });
});

describe('Error Handling', () => {
  it('should handle markdown processing errors gracefully', () => {
    // Mock error in markdown processing
    const originalConsoleError = console.error;
    console.error = jest.fn();
    
    const invalidMarkdown = null as any;
    const html = enhancedMarkdownToHtml(invalidMarkdown);
    
    expect(html).toContain('Error rendering markdown');
    expect(console.error).toHaveBeenCalled();
    
    console.error = originalConsoleError;
  });

  it('should handle math rendering errors', () => {
    const katex = require('katex');
    katex.renderToString.mockImplementation(() => {
      throw new Error('Math error');
    });
    
    const markdown = '$invalid math$';
    const html = enhancedMarkdownToHtml(markdown);
    
    expect(html).toContain('Math Error');
  });
});
