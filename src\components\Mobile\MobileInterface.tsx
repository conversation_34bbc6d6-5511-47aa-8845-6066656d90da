/**
 * Completely Redesigned Mobile Interface
 * Optimized for mobile screens with proper responsive design
 */

'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useApp } from '@/contexts/AppContext';
import { useScrollSync } from '@/hooks/useScrollSync';
import { EditorPane } from '../Editor/EditorPane';
import { PreviewPane } from '../Preview/PreviewPane';

interface MobileInterfaceProps {
  // Props if needed
}

export function MobileInterface({}: MobileInterfaceProps) {
  const { state, updateContent } = useApp();
  const [activeTab, setActiveTab] = useState<'edit' | 'preview'>('edit');
  const [showToolbar, setShowToolbar] = useState(true);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const { registerElement } = useScrollSync();
  const containerRef = useRef<HTMLDivElement>(null);

  // Detect keyboard visibility for mobile optimization
  useEffect(() => {
    const handleResize = () => {
      if (typeof window !== 'undefined') {
        const viewportHeight = window.visualViewport?.height || window.innerHeight;
        const windowHeight = window.innerHeight;
        const keyboardVisible = viewportHeight < windowHeight * 0.75;
        setIsKeyboardVisible(keyboardVisible);
      }
    };

    if (typeof window !== 'undefined' && window.visualViewport) {
      window.visualViewport.addEventListener('resize', handleResize);
      return () => window.visualViewport.removeEventListener('resize', handleResize);
    } else if (typeof window !== 'undefined') {
      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  }, []);

  const insertText = (before: string, after: string = '') => {
    const textarea = document.querySelector('textarea') as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = textarea.value.substring(start, end);
    const newText = before + selectedText + after;

    const newContent =
      textarea.value.substring(0, start) +
      newText +
      textarea.value.substring(end);

    updateContent(newContent);

    // Set cursor position
    setTimeout(() => {
      textarea.focus();
      const newCursorPos = start + before.length + selectedText.length + after.length;
      textarea.setSelectionRange(newCursorPos, newCursorPos);
    }, 0);
  };

  // Quick actions for mobile toolbar
  const quickActions = [
    { icon: 'B', label: 'Bold', action: () => insertText('**', '**') },
    { icon: 'I', label: 'Italic', action: () => insertText('*', '*') },
    { icon: 'H1', label: 'Header', action: () => insertText('# ', '') },
    { icon: '🔗', label: 'Link', action: () => insertText('[', '](url)') },
    { icon: '`', label: 'Code', action: () => insertText('`', '`') },
    { icon: '•', label: 'List', action: () => insertText('- ', '') },
  ];

  return (
    <div 
      ref={containerRef}
      className="h-full w-full flex flex-col bg-gray-50 dark:bg-gray-900 overflow-hidden"
      style={{ height: '100vh', height: '100dvh' }}
    >
      {/* Mobile Header with Tab Switcher */}
      <div className="flex-shrink-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm">
        {/* Tab Switcher */}
        <div className="flex">
          <button
            onClick={() => setActiveTab('edit')}
            className={`flex-1 flex items-center justify-center py-4 px-4 text-sm font-medium transition-all duration-200 ${
              activeTab === 'edit'
                ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 border-b-2 border-blue-600 dark:border-blue-400'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
            }`}
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            Edit
          </button>
          <button
            onClick={() => setActiveTab('preview')}
            className={`flex-1 flex items-center justify-center py-4 px-4 text-sm font-medium transition-all duration-200 ${
              activeTab === 'preview'
                ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 border-b-2 border-blue-600 dark:border-blue-400'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
            }`}
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
            Preview
          </button>
        </div>

        {/* Mobile Toolbar - Only show when editing and keyboard not visible */}
        {activeTab === 'edit' && showToolbar && !isKeyboardVisible && (
          <div className="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
            <div className="flex gap-1 p-2 overflow-x-auto">
              {quickActions.map((action, index) => (
                <button
                  key={index}
                  onClick={action.action}
                  className="flex-shrink-0 flex flex-col items-center gap-1 p-2 bg-white dark:bg-gray-600 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-500 transition-colors min-w-[50px] shadow-sm"
                >
                  <span className="text-sm font-mono">{action.icon}</span>
                  <span className="text-xs text-gray-600 dark:text-gray-400">{action.label}</span>
                </button>
              ))}
            </div>
            
            {/* Toolbar Toggle */}
            <div className="flex justify-center py-1">
              <button
                onClick={() => setShowToolbar(!showToolbar)}
                className="w-8 h-1 bg-gray-300 dark:bg-gray-600 rounded-full hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors"
              />
            </div>
          </div>
        )}

        {/* Collapsed Toolbar Indicator */}
        {activeTab === 'edit' && !showToolbar && !isKeyboardVisible && (
          <div className="flex justify-center py-2 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={() => setShowToolbar(true)}
              className="flex items-center gap-2 px-3 py-1 text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
              </svg>
              Show Toolbar
            </button>
          </div>
        )}
      </div>

      {/* Main Content Area */}
      <div className="flex-1 min-h-0 overflow-hidden">
        <div className="h-full w-full">
          {activeTab === 'edit' ? (
            <div className="h-full w-full">
              <EditorPane 
                scrollRegister={registerElement} 
                className="h-full w-full"
              />
            </div>
          ) : (
            <div className="h-full w-full overflow-auto">
              <PreviewPane 
                scrollRegister={registerElement} 
                className="h-full w-full"
              />
            </div>
          )}
        </div>
      </div>

      {/* Floating Action Button for Quick Tab Switch */}
      {!isKeyboardVisible && (
        <button
          onClick={() => setActiveTab(activeTab === 'edit' ? 'preview' : 'edit')}
          className="fixed bottom-6 right-6 w-14 h-14 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center z-50"
        >
          {activeTab === 'edit' ? (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
          ) : (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
          )}
        </button>
      )}

      {/* Safe Area for iOS */}
      <div 
        className="flex-shrink-0 bg-white dark:bg-gray-800" 
        style={{ 
          height: 'env(safe-area-inset-bottom)',
          minHeight: '0px'
        }} 
      />
    </div>
  );
}
