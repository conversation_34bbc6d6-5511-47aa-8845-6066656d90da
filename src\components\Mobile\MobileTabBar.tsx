/**
 * Framework7-inspired mobile tab bar component
 */

'use client';

import React from 'react';

interface MobileTabBarProps {
  activeTab: 'edit' | 'preview';
  onTabChange: (tab: 'edit' | 'preview') => void;
}

export function MobileTabBar({ activeTab, onTabChange }: MobileTabBarProps) {
  return (
    <div className="relative bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
      {/* Tab Bar Container */}
      <div className="flex relative">
        {/* Edit Tab */}
        <button
          onClick={() => onTabChange('edit')}
          className={`flex-1 flex items-center justify-center py-4 px-6 transition-all duration-300 relative ${
            activeTab === 'edit'
              ? 'text-blue-600 dark:text-blue-400'
              : 'text-gray-600 dark:text-gray-400'
          }`}
        >
          <div className="flex items-center gap-2">
            <svg 
              className={`w-5 h-5 transition-transform duration-300 ${
                activeTab === 'edit' ? 'scale-110' : 'scale-100'
              }`} 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={activeTab === 'edit' ? 2.5 : 2} 
                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" 
              />
            </svg>
            <span className={`font-medium transition-all duration-300 ${
              activeTab === 'edit' ? 'font-semibold' : 'font-medium'
            }`}>
              Edit
            </span>
          </div>
          
          {/* Active indicator */}
          {activeTab === 'edit' && (
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-12 h-1 bg-blue-600 dark:bg-blue-400 rounded-full transition-all duration-300" />
          )}
        </button>

        {/* Preview Tab */}
        <button
          onClick={() => onTabChange('preview')}
          className={`flex-1 flex items-center justify-center py-4 px-6 transition-all duration-300 relative ${
            activeTab === 'preview'
              ? 'text-blue-600 dark:text-blue-400'
              : 'text-gray-600 dark:text-gray-400'
          }`}
        >
          <div className="flex items-center gap-2">
            <svg 
              className={`w-5 h-5 transition-transform duration-300 ${
                activeTab === 'preview' ? 'scale-110' : 'scale-100'
              }`} 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={activeTab === 'preview' ? 2.5 : 2} 
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" 
              />
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={activeTab === 'preview' ? 2.5 : 2} 
                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" 
              />
            </svg>
            <span className={`font-medium transition-all duration-300 ${
              activeTab === 'preview' ? 'font-semibold' : 'font-medium'
            }`}>
              Preview
            </span>
          </div>
          
          {/* Active indicator */}
          {activeTab === 'preview' && (
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-12 h-1 bg-blue-600 dark:bg-blue-400 rounded-full transition-all duration-300" />
          )}
        </button>
      </div>

      {/* Background sliding indicator */}
      <div 
        className={`absolute top-0 bottom-0 w-1/2 bg-blue-50 dark:bg-blue-900/20 transition-transform duration-300 ease-out ${
          activeTab === 'edit' ? 'transform translate-x-0' : 'transform translate-x-full'
        }`}
        style={{ zIndex: -1 }}
      />
    </div>
  );
}
