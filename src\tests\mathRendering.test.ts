/**
 * Test suite for KaTeX math rendering functionality
 * Ensures math expressions are properly rendered with correct styling
 */

import { describe, it, expect, beforeAll } from '@jest/test-globals';
import { processEnhancedMarkdown } from '../utils/enhancedMarkdownProcessor';

// Mock KaTeX for testing
jest.mock('katex', () => ({
  renderToString: jest.fn((math: string, options: any) => {
    // Simulate KaTeX rendering
    if (math.includes('invalid')) {
      throw new Error('Invalid math expression');
    }
    return `<span class="katex"><span class="katex-mathml"><math><semantics><mrow><mi>${math}</mi></mrow></semantics></math></span><span class="katex-html">${math}</span></span>`;
  })
}));

// Test math expressions
const inlineMath = 'This is inline math: $E = mc^2$ and more text.';
const displayMath = `
This is display math:

$$\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}$$

And more content.
`;

const complexMath = `
# Math Examples

Inline: $\\alpha + \\beta = \\gamma$

Display:
$$\\sum_{i=1}^{n} i = \\frac{n(n+1)}{2}$$

Matrix:
$$\\begin{pmatrix}
a & b \\\\
c & d
\\end{pmatrix}$$

Fractions: $\\frac{a}{b} + \\frac{c}{d} = \\frac{ad + bc}{bd}$
`;

const invalidMath = `
Valid math: $x + y = z$

Invalid math: $invalid syntax here$

More valid: $$\\sum x_i$$
`;

const mixedContent = `
# Document with Math

Regular **bold** and *italic* text.

Inline math: $f(x) = x^2 + 2x + 1$

Code block:
\`\`\`javascript
function calculate() {
  return x * y;
}
\`\`\`

Display math:
$$f'(x) = 2x + 2$$

| Function | Derivative |
|----------|------------|
| $x^2$ | $2x$ |
| $\\sin(x)$ | $\\cos(x)$ |

More text with $\\pi \\approx 3.14159$.
`;

describe('KaTeX Math Rendering', () => {
  beforeAll(() => {
    // Ensure KaTeX mock is properly set up
    const katex = require('katex');
    expect(katex.renderToString).toBeDefined();
  });

  describe('Inline Math', () => {
    it('should render inline math expressions', () => {
      const html = processEnhancedMarkdown(inlineMath);
      
      // Should contain math wrapper
      expect(html).toContain('math-inline');
      expect(html).toContain('katex-inline');
      
      // Should contain KaTeX classes
      expect(html).toContain('katex');
      expect(html).toContain('katex-html');
      
      // Should preserve surrounding text
      expect(html).toContain('This is inline math:');
      expect(html).toContain('and more text.');
      
      // Should not contain raw dollar signs
      expect(html).not.toContain('$E = mc^2$');
    });

    it('should handle multiple inline math expressions', () => {
      const multipleInline = 'First: $a + b$ and second: $c - d$ expressions.';
      const html = processEnhancedMarkdown(multipleInline);
      
      // Should have multiple math spans
      const mathSpans = html.match(/math-inline/g);
      expect(mathSpans).toHaveLength(2);
      
      // Should contain both expressions
      expect(html).toContain('a + b');
      expect(html).toContain('c - d');
    });
  });

  describe('Display Math', () => {
    it('should render display math expressions', () => {
      const html = processEnhancedMarkdown(displayMath);
      
      // Should contain display math wrapper
      expect(html).toContain('math-display');
      expect(html).toContain('katex-display');
      
      // Should contain KaTeX classes
      expect(html).toContain('katex');
      expect(html).toContain('katex-html');
      
      // Should preserve surrounding content
      expect(html).toContain('This is display math:');
      expect(html).toContain('And more content.');
      
      // Should not contain raw dollar signs
      expect(html).not.toContain('$$\\int');
    });

    it('should handle complex display math', () => {
      const html = processEnhancedMarkdown(complexMath);
      
      // Should have multiple display math blocks
      const displayBlocks = html.match(/math-display/g);
      expect(displayBlocks?.length).toBeGreaterThanOrEqual(2);
      
      // Should contain complex expressions
      expect(html).toContain('\\sum_{i=1}^{n}');
      expect(html).toContain('\\begin{pmatrix}');
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid math expressions gracefully', () => {
      const html = processEnhancedMarkdown(invalidMath);
      
      // Should contain error styling
      expect(html).toContain('math-error');
      
      // Should still render valid math
      expect(html).toContain('math-inline');
      expect(html).toContain('math-display');
      
      // Should preserve text content
      expect(html).toContain('Valid math:');
      expect(html).toContain('More valid:');
    });

    it('should handle empty math expressions', () => {
      const emptyMath = 'Empty inline: $$ and empty display: $$$$';
      const html = processEnhancedMarkdown(emptyMath);
      
      // Should handle empty expressions
      expect(html).toContain('math-error');
      expect(html).toContain('Empty math expression');
    });
  });

  describe('Mixed Content', () => {
    it('should render math alongside other markdown features', () => {
      const html = processEnhancedMarkdown(mixedContent);
      
      // Should contain math
      expect(html).toContain('math-inline');
      expect(html).toContain('math-display');
      
      // Should contain other markdown features
      expect(html).toContain('<strong>bold</strong>');
      expect(html).toContain('<em>italic</em>');
      expect(html).toContain('<code>');
      expect(html).toContain('markdown-table');
      
      // Should contain math in table
      expect(html).toContain('x^2');
      expect(html).toContain('\\sin(x)');
    });

    it('should preserve math in complex structures', () => {
      const mathInList = `
- Item with math: $x + y$
- Another item: $a^2 + b^2 = c^2$

1. Numbered with math: $\\pi r^2$
2. Another: $e^{i\\pi} + 1 = 0$
      `;
      
      const html = processEnhancedMarkdown(mathInList);
      
      // Should contain list structure
      expect(html).toContain('<ul>');
      expect(html).toContain('<ol>');
      expect(html).toContain('<li>');
      
      // Should contain math in lists
      expect(html).toContain('math-inline');
      expect(html).toContain('x + y');
      expect(html).toContain('\\pi r^2');
    });
  });

  describe('Math Styling Classes', () => {
    it('should apply correct CSS classes for inline math', () => {
      const html = processEnhancedMarkdown(inlineMath);
      
      expect(html).toMatch(/<span class="math-inline katex-inline">/);
      expect(html).toContain('katex');
    });

    it('should apply correct CSS classes for display math', () => {
      const html = processEnhancedMarkdown(displayMath);
      
      expect(html).toMatch(/<div class="math-display katex-display">/);
      expect(html).toContain('katex');
    });

    it('should apply error classes for invalid math', () => {
      const html = processEnhancedMarkdown(invalidMath);
      
      expect(html).toMatch(/<span class="math-error">/);
      expect(html).toContain('Math Error:');
    });
  });

  describe('Special Math Symbols', () => {
    it('should handle common math macros', () => {
      const macroMath = `
Real numbers: $\\RR$
Natural numbers: $\\NN$
Integers: $\\ZZ$
Rationals: $\\QQ$
Complex: $\\CC$
      `;
      
      const html = processEnhancedMarkdown(macroMath);
      
      // Should contain math rendering
      expect(html).toContain('math-inline');
      
      // Should contain the macro symbols
      expect(html).toContain('\\RR');
      expect(html).toContain('\\NN');
      expect(html).toContain('\\ZZ');
    });

    it('should handle Greek letters and symbols', () => {
      const greekMath = `
Greek: $\\alpha, \\beta, \\gamma, \\delta$
Symbols: $\\infty, \\partial, \\nabla$
      `;
      
      const html = processEnhancedMarkdown(greekMath);
      
      expect(html).toContain('math-inline');
      expect(html).toContain('\\alpha');
      expect(html).toContain('\\infty');
    });
  });
});

// Export test utilities for manual testing
export const mathTestUtils = {
  inlineMath,
  displayMath,
  complexMath,
  mixedContent,
  
  testMathRendering() {
    console.log('Testing math rendering...');
    
    const tests = [
      { name: 'Inline Math', markdown: inlineMath },
      { name: 'Display Math', markdown: displayMath },
      { name: 'Complex Math', markdown: complexMath },
      { name: 'Mixed Content', markdown: mixedContent },
    ];
    
    tests.forEach(test => {
      console.log(`\n--- ${test.name} ---`);
      const html = processEnhancedMarkdown(test.markdown);
      console.log('HTML Output:', html);
      
      // Check for required classes
      const hasInlineMath = html.includes('math-inline');
      const hasDisplayMath = html.includes('math-display');
      const hasKatex = html.includes('katex');
      const hasErrors = html.includes('math-error');
      
      console.log('Validation:', {
        hasInlineMath,
        hasDisplayMath,
        hasKatex,
        hasErrors,
        isValid: (hasInlineMath || hasDisplayMath) && hasKatex && !hasErrors
      });
    });
  }
};
