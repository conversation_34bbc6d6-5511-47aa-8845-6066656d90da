@import "tailwindcss";

/* Custom CSS Variables */
:root {
  --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', <PERSON><PERSON><PERSON>, 'Courier New', monospace;
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* Base styles */
* {
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: var(--font-sans);
  background: var(--background);
  color: var(--foreground);
}

/* Typography */
.font-mono {
  font-family: var(--font-mono);
}

/* Dark mode support */
.dark {
  --background: #0a0a0a;
  --foreground: #ededed;
}

.dark body {
  background: var(--background);
  color: var(--foreground);
}

/* Prose styles for markdown preview */
.prose {
  color: inherit;
  max-width: none;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  color: inherit;
  font-weight: 600;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.prose h1 {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.prose h2 {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.prose h3 {
  font-size: 1.5rem;
  line-height: 2rem;
}

.prose h4 {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.prose h5 {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.prose h6 {
  font-size: 1rem;
  line-height: 1.5rem;
}

.prose p {
  margin-bottom: 1rem;
  line-height: 1.75;
}

.prose a {
  color: #3b82f6;
  text-decoration: none;
}

.prose a:hover {
  text-decoration: underline;
}

.prose strong {
  font-weight: 600;
}

.prose em {
  font-style: italic;
}

.prose code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-family: var(--font-jetbrains-mono);
}

.dark .prose code {
  background-color: #374151;
}

.prose pre {
  background-color: #f3f4f6;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.dark .prose pre {
  background-color: #1f2937;
}

.prose pre code {
  background: none;
  padding: 0;
}

.prose blockquote {
  border-left: 4px solid #e5e7eb;
  margin: 1rem 0;
  padding-left: 1rem;
  color: #6b7280;
  font-style: italic;
}

.dark .prose blockquote {
  border-left-color: #4b5563;
  color: #9ca3af;
}

.prose ul,
.prose ol {
  margin: 1rem 0;
  padding-left: 2rem;
}

.prose li {
  margin-bottom: 0.5rem;
}

.prose table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.prose th,
.prose td {
  border: 1px solid #e5e7eb;
  padding: 0.5rem;
  text-align: left;
}

.dark .prose th,
.dark .prose td {
  border-color: #4b5563;
}

.prose th {
  background-color: #f9fafb;
  font-weight: 600;
}

.dark .prose th {
  background-color: #374151;
}

.prose img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
}

.prose hr {
  border: none;
  border-top: 1px solid #e5e7eb;
  margin: 2rem 0;
}

.dark .prose hr {
  border-top-color: #4b5563;
}

/* Dark mode prose styles */
.dark .prose {
  color: #f3f4f6;
}

.dark .prose h1,
.dark .prose h2,
.dark .prose h3,
.dark .prose h4,
.dark .prose h5,
.dark .prose h6 {
  color: #f9fafb;
}

.dark .prose a {
  color: #60a5fa;
}

.dark .prose strong {
  color: #f9fafb;
}

/* Scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.dark ::-webkit-scrollbar-thumb {
  background: #4b5563;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  .print-full-width {
    width: 100% !important;
    max-width: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

/* Animation delay utilities */
.animation-delay-150 {
  animation-delay: 150ms;
}

.animation-delay-300 {
  animation-delay: 300ms;
}

.animation-delay-500 {
  animation-delay: 500ms;
}

/* Custom utility classes */
.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

.animate-slideIn {
  animation: slideIn 0.3s ease-out;
}

.animate-pulse-slow {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce-slow {
  animation: bounce 2s infinite;
}

/* Smooth transitions */
.transition-all-300 {
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-transform-300 {
  transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-opacity-200 {
  transition: opacity 200ms ease-in-out;
}

/* Hover effects */
.hover-scale {
  transition: transform 200ms ease-in-out;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-lift {
  transition: transform 200ms ease-in-out, box-shadow 200ms ease-in-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

.focus-ring-dark {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-gray-800;
}

/* Scrollbar utilities */
.scrollbar-none {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scrollbar-none::-webkit-scrollbar {
  display: none;
}

.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 2px;
}

.scrollbar-thumb-gray-600::-webkit-scrollbar-thumb {
  background-color: #4b5563;
  border-radius: 2px;
}

.dark .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
  background-color: #4b5563;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  /* Force dark mode and remove light mode */
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }

  body {
    background: var(--background) !important;
    color: var(--foreground) !important;
  }

  /* Ensure proper touch targets */
  button, .button, [role="button"] {
    min-height: 44px;
    min-width: 44px;
    padding: 12px 16px;
    touch-action: manipulation;
  }

  /* Mobile-first layout adjustments */
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Improve text readability on mobile */
  .prose {
    font-size: 16px;
    line-height: 1.6;
    padding: 1rem;
  }

  .prose h1 {
    font-size: 1.75rem;
    margin-top: 1.5rem;
    margin-bottom: 1rem;
  }
  .prose h2 {
    font-size: 1.5rem;
    margin-top: 1.25rem;
    margin-bottom: 0.75rem;
  }
  .prose h3 {
    font-size: 1.25rem;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
  }
  .prose h4 { font-size: 1.125rem; }
  .prose h5 { font-size: 1rem; }
  .prose h6 { font-size: 0.875rem; }

  /* Better spacing for mobile */
  .prose p {
    margin-bottom: 1.25rem;
  }

  .prose ul, .prose ol {
    padding-left: 1.5rem;
    margin: 1rem 0;
  }

  .prose li {
    margin-bottom: 0.75rem;
    line-height: 1.6;
  }

  /* Improve code blocks on mobile */
  .prose pre {
    font-size: 14px;
    padding: 0.75rem;
    margin: 1rem -1rem;
    border-radius: 0;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .prose code {
    font-size: 14px;
    padding: 0.25rem 0.375rem;
    word-break: break-word;
  }

  /* Better table handling on mobile */
  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    margin: 1rem -1rem;
  }

  .prose table {
    font-size: 14px;
    min-width: 100%;
    white-space: nowrap;
  }

  .prose th,
  .prose td {
    padding: 0.5rem 0.25rem;
    min-width: 100px;
    vertical-align: top;
  }

  /* Improve form elements */
  input, textarea, select {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 12px 16px;
    border-radius: 8px;
    border: 1px solid #374151;
    background: #1f2937;
    color: #f9fafb;
  }

  /* Better touch scrolling */
  .overflow-auto,
  .overflow-y-auto,
  .overflow-x-auto {
    -webkit-overflow-scrolling: touch;
  }

  /* Mobile-specific editor improvements */
  .editor-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
  }

  .editor-toolbar {
    flex-shrink: 0;
    padding: 0.5rem;
    background: #1f2937;
    border-bottom: 1px solid #374151;
  }

  .editor-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
  }

  .editor-panes {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
  }

  /* Mobile editor and preview layout */
  .editor-pane,
  .preview-pane {
    flex: 1;
    min-height: 50vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Mobile-specific button styles */
  .mobile-button {
    padding: 12px 20px;
    font-size: 16px;
    border-radius: 8px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  /* Improve blockquotes on mobile */
  .prose blockquote {
    margin: 1rem 0;
    padding: 1rem;
    border-left: 4px solid #3b82f6;
    background: #1f2937;
    border-radius: 0 8px 8px 0;
  }

  /* Better image handling on mobile */
  .prose img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 1rem 0;
  }

  /* Improve horizontal rules */
  .prose hr {
    margin: 2rem 0;
    border: none;
    border-top: 2px solid #374151;
  }

  /* Mobile-specific link styles */
  .prose a {
    color: #60a5fa;
    text-decoration: underline;
    word-break: break-word;
  }

  /* Better focus states for mobile */
  button:focus,
  input:focus,
  textarea:focus,
  select:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }

  /* Improve sidebar on mobile */
  .sidebar {
    width: 100%;
    height: auto;
    position: relative;
  }

  /* Mobile-specific animations */
  .mobile-fade-in {
    animation: fadeIn 0.3s ease-out;
  }

  /* Improve markdown list rendering */
  .md-list {
    margin: 1rem 0;
    padding-left: 1.5rem;
  }

  .md-list-item {
    margin-bottom: 0.75rem;
    line-height: 1.6;
  }

  /* Better table styling for mobile */
  .md-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    background: #1f2937;
  }

  .md-table-cell {
    padding: 0.75rem 0.5rem;
    border: 1px solid #374151;
    vertical-align: top;
  }

  .md-table-header-cell {
    background: #374151;
    font-weight: 600;
    color: #f9fafb;
  }

  /* Text alignment classes for mobile */
  .text-left { text-align: left; }
  .text-center { text-align: center; }
  .text-right { text-align: right; }
}

/* Enhanced markdown rendering styles */
.md-paragraph {
  margin-bottom: 1rem;
  line-height: 1.75;
}

.md-blockquote {
  border-left: 4px solid #3b82f6;
  margin: 1rem 0;
  padding: 1rem;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 0 8px 8px 0;
  font-style: italic;
}

.md-hr {
  border: none;
  border-top: 2px solid #374151;
  margin: 2rem 0;
}

.md-strikethrough {
  text-decoration: line-through;
  opacity: 0.7;
}

/* Enhanced markdown text formatting */
.md-bold {
  font-weight: 600;
}

.md-italic {
  font-style: italic;
}

.md-highlight {
  background-color: #fef08a;
  color: #854d0e;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

.dark .md-highlight {
  background-color: #a16207;
  color: #fef3c7;
}

.md-inserted {
  text-decoration: underline;
  text-decoration-color: #10b981;
  text-decoration-thickness: 2px;
}

.md-subscript {
  font-size: 0.75em;
  vertical-align: sub;
}

.md-superscript {
  font-size: 0.75em;
  vertical-align: super;
}

.md-link {
  color: #3b82f6;
  text-decoration: none;
  transition: color 0.2s ease;
}

.md-link:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

.dark .md-link {
  color: #60a5fa;
}

.dark .md-link:hover {
  color: #93c5fd;
}

.md-heading {
  position: relative;
}

.anchor-link {
  opacity: 0;
  margin-left: 0.5rem;
  color: #6b7280;
  text-decoration: none;
  font-weight: normal;
  transition: opacity 0.2s ease;
}

.md-heading:hover .anchor-link {
  opacity: 1;
}

.anchor-link:hover {
  color: #3b82f6;
}

/* Advanced markdown features styles */

/* Task lists */
.task-list-checkbox {
  margin-right: 0.5rem;
  accent-color: #3b82f6;
}

/* Keyboard keys */
.md-kbd {
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  padding: 0.125rem 0.375rem;
  font-size: 0.875rem;
  font-family: var(--font-mono);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.dark .md-kbd {
  background-color: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

/* Footnotes */
.md-footnote-ref {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 600;
}

.md-footnote-ref:hover {
  text-decoration: underline;
}

.md-footnotes {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
  font-size: 0.875rem;
}

.dark .md-footnotes {
  border-top-color: #4b5563;
}

/* Definition lists */
.md-definition-list {
  margin: 1rem 0;
}

.md-definition-term {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.md-definition-description {
  margin-left: 1.5rem;
  margin-bottom: 1rem;
  color: #6b7280;
}

.dark .md-definition-description {
  color: #9ca3af;
}

/* Abbreviations */
.md-abbr {
  border-bottom: 1px dotted #6b7280;
  cursor: help;
  text-decoration: none;
}

.dark .md-abbr {
  border-bottom-color: #9ca3af;
}

/* Custom containers */
.custom-container {
  margin: 1rem 0;
  padding: 1rem;
  border-radius: 0.5rem;
  border-left: 4px solid;
}

.custom-container-title {
  font-weight: 600;
  margin-bottom: 0.5rem;
  margin-top: 0;
}

.custom-container.warning {
  background-color: #fef3c7;
  border-left-color: #f59e0b;
  color: #92400e;
}

.dark .custom-container.warning {
  background-color: #451a03;
  color: #fbbf24;
}

.custom-container.info {
  background-color: #dbeafe;
  border-left-color: #3b82f6;
  color: #1e40af;
}

.dark .custom-container.info {
  background-color: #1e3a8a;
  color: #93c5fd;
}

.custom-container.tip {
  background-color: #d1fae5;
  border-left-color: #10b981;
  color: #065f46;
}

.dark .custom-container.tip {
  background-color: #064e3b;
  color: #6ee7b7;
}

.custom-container.danger {
  background-color: #fee2e2;
  border-left-color: #ef4444;
  color: #991b1b;
}

.dark .custom-container.danger {
  background-color: #7f1d1d;
  color: #fca5a5;
}

/* Enhanced emoji support */
.emoji {
  font-size: 1.2em;
  vertical-align: middle;
}

/* Better spacing for nested elements */
.md-paragraph + .md-paragraph {
  margin-top: 1rem;
}

.md-list .md-paragraph {
  margin-bottom: 0.5rem;
}

/* Improved table responsiveness */
.table-container {
  overflow-x: auto;
  margin: 1rem 0;
  border-radius: 8px;
  border: 1px solid #374151;
  max-width: 100%;
}

@media (max-width: 768px) {
  .table-container {
    font-size: 0.875rem;
  }

  .md-table-cell {
    padding: 0.5rem 0.25rem;
  }
}

/* Print styles for advanced features */
@media print {
  .custom-container {
    border: 1px solid #000;
    break-inside: avoid;
  }

  .md-footnotes {
    page-break-before: always;
  }

  .task-list-checkbox {
    -webkit-appearance: checkbox;
    appearance: checkbox;
  }
}

/* Enhanced responsive table styles */
.table-responsive-wrapper {
  overflow-x: auto;
  margin: 1.5rem 0;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  background: white;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  -webkit-overflow-scrolling: touch;
}

.dark .table-responsive-wrapper {
  border-color: #374151;
  background: #1f2937;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
}

.markdown-table {
  width: 100%;
  min-width: 500px;
  border-collapse: collapse;
  background: inherit;
  font-size: 0.875rem;
  line-height: 1.5;
}

.markdown-table-header {
  background: #f8f9fa;
  position: sticky;
  top: 0;
  z-index: 10;
}

.dark .markdown-table-header {
  background: #374151;
}

.markdown-table-body {
  background: inherit;
}

.markdown-table-header-cell {
  padding: 0.75rem;
  border-bottom: 2px solid #dee2e6;
  border-right: 1px solid #dee2e6;
  font-weight: 600;
  color: #495057;
  text-align: left;
  white-space: nowrap;
  min-width: 100px;
}

.dark .markdown-table-header-cell {
  border-color: #4b5563;
  color: #f9fafb;
}

.markdown-table-cell {
  padding: 0.75rem;
  border-bottom: 1px solid #e9ecef;
  border-right: 1px solid #e9ecef;
  vertical-align: top;
  word-wrap: break-word;
  max-width: 300px;
}

.dark .markdown-table-cell {
  border-color: #4b5563;
}

.markdown-table tbody tr:hover {
  background: #f8f9fa;
}

.dark .markdown-table tbody tr:hover {
  background: #374151;
}

/* Text alignment classes for tables */
.text-align-left { text-align: left !important; }
.text-align-center { text-align: center !important; }
.text-align-right { text-align: right !important; }

/* Mobile table optimizations */
@media (max-width: 768px) {
  .table-responsive-wrapper {
    margin: 1rem 0;
    border-radius: 6px;
    font-size: 0.8rem;
  }

  .markdown-table {
    min-width: 600px;
    font-size: 0.8rem;
  }

  .markdown-table-header-cell,
  .markdown-table-cell {
    padding: 0.5rem;
    min-width: 80px;
  }

  .markdown-table-cell {
    max-width: 200px;
  }
}

/* Print styles for tables */
@media print {
  .table-responsive-wrapper {
    overflow: visible;
    border: none;
    box-shadow: none;
    margin: 1rem 0;
  }

  .markdown-table {
    min-width: auto;
    width: 100%;
    font-size: 10pt;
    page-break-inside: avoid;
  }

  .markdown-table-header-cell,
  .markdown-table-cell {
    border: 0.5pt solid #000;
    padding: 6pt;
  }

  .markdown-table-header {
    background: #f0f0f0 !important;
  }

  .markdown-table tbody tr:hover {
    background: transparent !important;
  }
}

/* Enhanced list styles */
.md-list {
  margin: 1rem 0;
  padding-left: 2rem;
}

.md-list-item {
  margin-bottom: 0.5rem;
  line-height: 1.6;
}

/* Code block enhancements */
.code-block-wrapper {
  margin: 1rem 0;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #374151;
}

.code-block-header {
  background: #374151;
  padding: 0.5rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
}

.code-language {
  color: #9ca3af;
  font-weight: 500;
}

.copy-code-btn {
  background: #4b5563;
  color: #f9fafb;
  border: none;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.copy-code-btn:hover {
  background: #6b7280;
}

/* Mark and ins elements */
mark {
  background-color: #fbbf24;
  color: #1f2937;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

ins {
  background-color: #10b981;
  color: #1f2937;
  text-decoration: none;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

/* Subscript and superscript */
sub, sup {
  font-size: 0.75em;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/* Tablet optimizations */
@media (min-width: 769px) and (max-width: 1024px) {
  .prose {
    font-size: 15px;
  }

  /* Adjust button sizes for tablet */
  button, .button {
    min-height: 40px;
    min-width: 40px;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* Ensure crisp text rendering */
  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Enhanced Mobile optimizations */
@media (max-width: 768px) {
  .prose {
    font-size: 0.9rem;
    line-height: 1.5;
    padding: 0.5rem;
  }

  .prose h1 { font-size: 1.75rem; margin-top: 1.5rem; margin-bottom: 0.75rem; }
  .prose h2 { font-size: 1.5rem; margin-top: 1.25rem; margin-bottom: 0.5rem; }
  .prose h3 { font-size: 1.25rem; margin-top: 1rem; margin-bottom: 0.5rem; }
  .prose h4 { font-size: 1.1rem; margin-top: 0.75rem; margin-bottom: 0.25rem; }

  .code-block-wrapper {
    margin: 1rem -0.5rem;
    border-radius: 0.25rem;
    font-size: 0.8rem;
  }

  .code-block-header {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
  }

  .copy-code-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.7rem;
  }

  .md-table {
    font-size: 0.8rem;
  }

  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    margin: 0 -0.5rem;
  }

  .md-table-cell {
    padding: 0.5rem 0.25rem;
    min-width: 80px;
  }

  /* Task list checkboxes */
  .task-list-checkbox {
    width: 1rem;
    height: 1rem;
    margin-right: 0.5rem;
  }

  /* Custom containers */
  .custom-container {
    margin: 1rem -0.5rem;
    padding: 0.75rem;
    border-radius: 0.25rem;
  }

  .custom-container-title {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }

  /* Keyboard keys */
  .md-kbd {
    font-size: 0.7rem;
    padding: 0.125rem 0.25rem;
  }

  /* Blockquotes */
  .md-blockquote {
    margin: 1rem 0;
    padding-left: 0.75rem;
    border-left-width: 3px;
  }

  /* Lists */
  .md-list {
    padding-left: 1rem;
  }

  .md-list-item {
    margin: 0.25rem 0;
  }
}

/* Touch-friendly improvements */
@media (max-width: 768px) and (pointer: coarse) {
  .copy-code-btn {
    min-height: 44px;
    min-width: 44px;
    padding: 0.5rem;
  }

  .task-list-checkbox {
    min-height: 44px;
    min-width: 44px;
  }

  /* Increase tap targets */
  .prose a {
    padding: 0.125rem 0;
    margin: -0.125rem 0;
  }

  /* Make buttons more touch-friendly */
  button {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improve text selection on mobile */
  .prose {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }

  /* Better scrolling on mobile */
  .overflow-y-auto {
    -webkit-overflow-scrolling: touch;
  }
}

/* Additional mobile optimizations */
@media (max-width: 480px) {
  .prose {
    font-size: 0.85rem;
    padding: 0.25rem;
  }

  .prose h1 { font-size: 1.5rem; }
  .prose h2 { font-size: 1.3rem; }
  .prose h3 { font-size: 1.15rem; }
  .prose h4 { font-size: 1rem; }

  .code-block-wrapper {
    margin: 0.75rem -0.25rem;
    font-size: 0.75rem;
  }

  .md-table-cell {
    padding: 0.25rem 0.125rem;
    font-size: 0.75rem;
  }

  .custom-container {
    margin: 0.75rem -0.25rem;
    padding: 0.5rem;
  }
}

/* Mobile zoom and viewport optimizations */
@media (max-width: 768px) {
  /* Prevent zoom on input focus */
  input, textarea, select {
    font-size: 16px !important;
  }

  /* Improve touch scrolling */
  * {
    -webkit-overflow-scrolling: touch;
  }

  /* Better text selection on mobile */
  .prose, .editor-content {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
    -webkit-touch-callout: default;
  }

  /* Prevent horizontal scroll */
  body {
    overflow-x: hidden;
  }

  /* Mobile-friendly status bar */
  .status-bar {
    height: 32px;
    padding: 0 8px;
    font-size: 11px;
  }

  /* Better mobile layout */
  .layout-container {
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for mobile */
  }

  /* Improve mobile editor */
  .editor-pane {
    font-size: 14px;
    line-height: 1.5;
  }

  /* Mobile-friendly buttons */
  .mobile-button {
    min-height: 44px;
    min-width: 44px;
    padding: 12px;
    font-size: 14px;
  }

  /* Prevent text size adjustment */
  html {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
  }
}

/* Landscape mobile optimizations */
@media (max-width: 768px) and (orientation: landscape) {
  .status-bar {
    height: 24px;
    font-size: 10px;
  }

  .layout-container {
    height: 100vh;
    height: 100dvh;
  }
}

/* High DPI mobile displays */
@media (max-width: 768px) and (-webkit-min-device-pixel-ratio: 2) {
  .prose {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Sharper icons on retina mobile */
  svg {
    shape-rendering: geometricPrecision;
  }
}
